# 🔑 Vercel Environment Variables Setup

## ⚠️ IMPORTANT: Exact Variable Names Required

Your React components use specific environment variable names. You **MUST** use these exact names in Vercel, or your app will not work properly.

## 🚀 Required Environment Variables for Vercel

Copy and paste these into your Vercel project settings under **Project Settings → Environment Variables**:

### 1. API Configuration
```
VITE_APP_API_BASE_URL=https://api.visionmaxfleet.com/V2
```
**Used in:** `src/api/apiClient.ts`, `src/api/axiosDefault.ts`, `src/utils/env.ts`

### 2. Google Maps API Key
```
VITE_APP_GOOGLE_MAPS_API_KEY=your_actual_google_maps_api_key
```
**Used in:** `src/components/charts/LocationHeatMap.tsx`, `src/components/maps/TripRouteMap.tsx`

### 3. Stripe Publishable Key
```
VITE_STRIPE_PUBLISHABLE_KEY=your_actual_stripe_publishable_key
```
**Used in:** `src/features/payment/SubscriptionPage.tsx`

### 4. Application Environment
```
VITE_APP_ENVIRONMENT=production
```

### 5. Feature Flags
```
VITE_APP_ENABLE_SOCKET_IO=true
VITE_APP_DEBUG_MODE=false
```
**Used in:** `src/utils/env.ts`

## 🔧 Optional Environment Variables

Add these if you use the corresponding features:

### WebSocket & Real-time Features
```
VITE_WEBSOCKET_URL=wss://api.visionmaxfleet.com/ws
```

### MQTT Configuration (for IoT devices)
```
VITE_MQTT_BROKER_URL=wss://mqtt.visionmaxfleet.com:8084/mqtt
VITE_MQTT_USERNAME=your_mqtt_username
VITE_MQTT_PASSWORD=your_mqtt_password
```

### Application Metadata
```
VITE_APP_NAME=DashFleet UI
VITE_APP_VERSION=1.0.0
VITE_API_TIMEOUT=10000
```

## 📋 Step-by-Step Vercel Setup

1. **Go to Vercel Dashboard**: [vercel.com/dashboard](https://vercel.com/dashboard)
2. **Import your project** from Git repository
3. **Configure Framework**: Select "Vite" as framework preset
4. **Set Root Directory**: `dashfleet-ui` (if your project is in a subdirectory)
5. **Add Environment Variables**:
   - Go to **Project Settings → Environment Variables**
   - Add each variable from the "Required" section above
   - Replace placeholder values with your actual API keys
6. **Deploy**: Click "Deploy" button

## ⚡ Quick Copy-Paste for Vercel

Here's a quick reference for copying into Vercel (replace with your actual values):

```
VITE_APP_API_BASE_URL=https://api.visionmaxfleet.com/V2
VITE_APP_GOOGLE_MAPS_API_KEY=your_actual_google_maps_api_key
VITE_STRIPE_PUBLISHABLE_KEY=your_actual_stripe_publishable_key
VITE_APP_ENVIRONMENT=production
VITE_APP_ENABLE_SOCKET_IO=true
VITE_APP_DEBUG_MODE=false
```

## 🔍 How to Get Your API Keys

### Google Maps API Key:
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Enable Maps JavaScript API and Places API
3. Create credentials → API Key
4. Restrict the key to your domain for security

### Stripe Publishable Key:
1. Go to [Stripe Dashboard](https://dashboard.stripe.com/)
2. Navigate to Developers → API Keys
3. Copy the "Publishable key" (starts with `pk_`)

## ❌ Common Mistakes to Avoid

1. **Wrong variable names**: Using `VITE_API_BASE_URL` instead of `VITE_APP_API_BASE_URL`
2. **Missing VITE_ prefix**: All environment variables must start with `VITE_`
3. **Forgetting to set environment**: Not setting `VITE_APP_ENVIRONMENT=production`
4. **Using test keys in production**: Make sure to use production API keys

## ✅ Verification

After deployment, check your browser's developer console. If you see errors like:
- "Google Maps API key is missing"
- "Stripe publishable key is required"
- API calls failing with 401/403 errors

Then double-check your environment variable names and values in Vercel.

## 🆘 Troubleshooting

If your app doesn't work after deployment:

1. **Check Vercel build logs** for environment variable errors
2. **Verify variable names** match exactly what's shown above
3. **Test API keys** work in your local environment first
4. **Check browser console** for JavaScript errors related to missing environment variables
