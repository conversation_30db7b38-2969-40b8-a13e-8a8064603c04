import React, { useEffect, useRef, useState } from 'react';
import { Spinner } from '../common/Spinner';

// Event type definitions
export interface MapEvent {
  id: string;
  name: string,
  type: 'stop' | 'hardBrake' | 'acceleration' | 'speeding' | 'turn' | 'collision' | 'custom';
  position: {
    lat: number;
    lng: number;
  };
  timestamp: string;
  deviceId: string;
  driverId?: string;
  severity?: 'low' | 'medium' | 'high';
  details?: string;
  ticket?: string;
  type_name?: string;
  severity_level?: number;
  driver_name?: string;
  device_cid?: string;
  lat?: number;
  lng?: number;
  trip_id?: number;
  [key: string]: any;
}

interface EventsMapProps {
  events: MapEvent[];
  selectedEventId?: string | null;
  onEventSelect?: (eventId: string) => void;
  center?: { lat: number; lng: number };
  zoom?: number;
  className?: string;
  maxMarkersBeforeClustering?: number;
}

declare global {
  interface Window {
    google: any;
    initMap: () => void;
    markerClusterer: any;
  }
}

// Map constants
const DEFAULT_CENTER = { lat: 39.8283, lng: -98.5795 }; // US center
const DEFAULT_ZOOM = 4;
const DEFAULT_MAX_MARKERS = 100; // Default threshold for clustering

export const EventsMap: React.FC<EventsMapProps> = ({
  events,
  selectedEventId,
  onEventSelect,
  center = DEFAULT_CENTER,
  zoom = DEFAULT_ZOOM,
  className = '',
  maxMarkersBeforeClustering = DEFAULT_MAX_MARKERS,
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const googleMapRef = useRef<any>(null);
  const markersRef = useRef<Map<string, any>>(new Map());
  const clustererRef = useRef<any>(null);
  const infoWindowRef = useRef<any>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedEvent, setSelectedEvent] = useState<MapEvent | null>(null);

  const apiKey = import.meta.env.VITE_APP_GOOGLE_MAPS_API_KEY;

  // Load Google Maps script (MarkerClusterer is optional)
  useEffect(() => {
    if (!apiKey) {
      setError('Google Maps API key is missing');
      return;
    }

    if (window.google) {
      setIsLoaded(true);
      return;
    }

    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=marker,geometry&v=weekly`;
    script.async = true;
    script.defer = true;
    
    script.onload = () => {
      console.log('✅ Google Maps API loaded');
      
      // Try to load MarkerClusterer, but don't fail if it doesn't work
      if (events.length > maxMarkersBeforeClustering) {
        const clustererScript = document.createElement('script');
        clustererScript.src = 'https://unpkg.com/@googlemaps/markerclusterer/dist/index.min.js';
        clustererScript.onload = () => {
          console.log('✅ MarkerClusterer loaded');
          setIsLoaded(true);
        };
        clustererScript.onerror = () => {
          console.warn('⚠️ MarkerClusterer failed to load, continuing without clustering');
          setIsLoaded(true);
        };
        document.head.appendChild(clustererScript);
      } else {
        // Don't need clustering for small datasets
        setIsLoaded(true);
      }
    };
    
    script.onerror = () => {
      console.error('❌ Failed to load Google Maps API');
      setError('Failed to load Google Maps API');
    };

    document.head.appendChild(script);

    return () => {
      if (script.parentNode) {
        script.parentNode.removeChild(script);
      }
    };
  }, [apiKey, events.length, maxMarkersBeforeClustering]);

  // Initialize map
  useEffect(() => {
    if (!isLoaded || !mapRef.current || !window.google) return;

    try {
      console.log('🗺️ Initializing Google Map for Events');
      
      const map = new window.google.maps.Map(mapRef.current, {
        center: center,
        zoom: zoom,
        mapId: 'EVENTS_MAP_ID', // Required for AdvancedMarkerElement
        mapTypeId: 'roadmap',
        disableDefaultUI: false,
        zoomControl: true,
        streetViewControl: false,
        mapTypeControl: true,
        fullscreenControl: true,
        clickableIcons: false,
      });

      googleMapRef.current = map;

      // Initialize InfoWindow
      infoWindowRef.current = new window.google.maps.InfoWindow({
        disableAutoPan: false,
        maxWidth: 350,
        pixelOffset: new window.google.maps.Size(0, -10)
      });

      // Add map click listener to close InfoWindow
      map.addListener('click', () => {
        console.log('🗺️ Map clicked - closing InfoWindow');
        if (infoWindowRef.current) {
          infoWindowRef.current.close();
        }
        setSelectedEvent(null);
        if (onEventSelect) {
          onEventSelect('');
        }
      });

      console.log('✅ Events Map initialized successfully');
    } catch (err) {
      console.error('❌ Error initializing events map:', err);
      setError(`Error initializing map: ${err}`);
    }
  }, [isLoaded, center, zoom]);

  // Create marker icon based on event type and severity
  const createEventMarkerIcon = (event: MapEvent) => {
    // Determine severity level
    const severity = event.severity || 
      (event.severity_level ? 
        (event.severity_level >= 7 ? 'high' : 
         event.severity_level >= 4 ? 'medium' : 'low') : 
        'low');

    // Define colors based on severity
    const severityColors = {
      'high': '#ef4444',    // red
      'medium': '#f59e0b',  // amber
      'low': '#10b981',     // green
    };

    const color = severityColors[severity] || '#6b7280'; // gray fallback

    // Define event type icons
    const eventTypeIcons = {
      'stop': '⏹️',
      'hardBrake': '🛑',
      'acceleration': '⚡',
      'speeding': '💨',
      'turn': '↻',
      'collision': '💥',
      'custom': '📍'
    };

    const icon = eventTypeIcons[event.type] || '📍';

    // Create a custom pin element
    const pinElement = document.createElement('div');
    pinElement.style.cssText = `
      width: 24px;
      height: 24px;
      background-color: ${color};
      border: 3px solid white;
      border-radius: 50%;
      box-shadow: 0 2px 6px rgba(0,0,0,0.4);
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      transition: transform 0.2s ease;
    `;
    
    pinElement.innerHTML = icon;
    
    // Add hover effect
    pinElement.addEventListener('mouseenter', () => {
      pinElement.style.transform = 'scale(1.2)';
    });
    
    pinElement.addEventListener('mouseleave', () => {
      pinElement.style.transform = 'scale(1)';
    });

    return pinElement;
  };

  // Format timestamp for display
  const formatTime = (timestamp: string | number) => {
    const date = new Date(typeof timestamp === 'number' ? timestamp * 1000 : timestamp);
    return date.toLocaleString();
  };

  // Get severity display info
  const getSeverityInfo = (event: MapEvent) => {
    const severityLevel = event.severity_level || 0;
    const severity = event.severity || 
      (severityLevel >= 7 ? 'high' : 
       severityLevel >= 4 ? 'medium' : 'low');

    const severityLabels = {
      'high': 'Severe',
      'medium': 'Moderate', 
      'low': 'Minor'
    };

    const severityColors = {
      'high': 'bg-red-100 text-red-800',
      'medium': 'bg-yellow-100 text-yellow-800',
      'low': 'bg-green-100 text-green-800'
    };

    return {
      label: severityLabels[severity] || 'Unknown',
      colorClass: severityColors[severity] || 'bg-gray-100 text-gray-800'
    };
  };

  // Create InfoWindow content
  const createInfoWindowContent = (event: MapEvent) => {
    const severityInfo = getSeverityInfo(event);
    const eventTypeDisplay = event.type_name || (event.type.charAt(0).toUpperCase() + event.type.slice(1));
    
    return `
      <div style="padding: 16px; min-width: 250px; font-family: Arial, sans-serif;" onclick="event.stopPropagation();">
        <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 12px;">
          <h3 style="margin: 0; font-size: 16px; font-weight: 600; color: #1f2937;">
            ${eventTypeDisplay} Event
          </h3>
          <span style="
            padding: 4px 8px; 
            border-radius: 12px; 
            font-size: 11px; 
            font-weight: 500;
            background-color: ${severityInfo.colorClass.includes('red') ? '#fee2e2' : 
                              severityInfo.colorClass.includes('yellow') ? '#fef3c7' : '#dcfce7'};
            color: ${severityInfo.colorClass.includes('red') ? '#991b1b' :
                    severityInfo.colorClass.includes('yellow') ? '#92400e' : '#166534'};
          ">
            ${severityInfo.label}
          </span>
        </div>
        
        <div style="space-y: 8px;">
          <div style="margin-bottom: 8px;">
            <strong style="color: #6b7280;">Time:</strong>
            <span style="margin-left: 8px; font-size: 14px;">${formatTime(event.timestamp)}</span>
          </div>
          
          <div style="margin-bottom: 8px;">
            <strong style="color: #6b7280;">Device:</strong>
            <span style="margin-left: 8px; font-size: 14px;">${event.name || 'Unknown'}</span>
          </div>
          
          ${event.driver_name ? `
            <div style="margin-bottom: 8px;">
              <strong style="color: #6b7280;">Driver:</strong>
              <span style="margin-left: 8px; font-size: 14px;">${event.driver_name}</span>
            </div>
          ` : ''}
          
          ${event.ticket ? `
            <div style="margin-bottom: 8px;">
              <strong style="color: #6b7280;">Event ID:</strong>
              <span style="margin-left: 8px; font-size: 14px; font-family: monospace;">${event.ticket}</span>
            </div>
          ` : ''}
          
          <div style="margin-bottom: 8px;">
            <strong style="color: #6b7280;">Location:</strong>
            <span style="margin-left: 8px; font-size: 14px;">
              ${event.position.lat.toFixed(6)}, ${event.position.lng.toFixed(6)}
            </span>
          </div>
          
          ${event.details ? `
            <div style="margin-bottom: 12px;">
              <strong style="color: #6b7280;">Details:</strong>
              <span style="margin-left: 8px; font-size: 14px;">${event.details}</span>
            </div>
          ` : ''}
        </div>
        
        <div style="margin-top: 16px; padding-top: 12px; border-top: 1px solid #e5e7eb;">
          <button 
            onclick="
              event.stopPropagation();
              window.dispatchEvent(new CustomEvent('eventSelect', { detail: '${event.id || event.ticket}' }));
            "
            style="
              width: 100%;
              padding: 8px 16px;
              background-color: #3b82f6;
              color: white;
              border: none;
              border-radius: 6px;
              font-size: 14px;
              font-weight: 500;
              cursor: pointer;
              transition: background-color 0.2s;
            "
            onmouseover="this.style.backgroundColor='#2563eb'"
            onmouseout="this.style.backgroundColor='#3b82f6'"
          >
            📊 View Event Details
          </button>
        </div>
      </div>
    `;
  };

  // Handle marker click
  const handleMarkerClick = (event: MapEvent, marker: any, clickEvent?: any) => {
    console.log(`🖱️ Event marker clicked: ${event.id || event.ticket}`);
    
    // Stop event propagation
    if (clickEvent) {
      clickEvent.stop?.();
      clickEvent.domEvent?.stopPropagation?.();
    }
    
    setSelectedEvent(event);
    
    if (onEventSelect) {
      onEventSelect(event.id || event.ticket || '');
    }

    // Show InfoWindow with delay for proper rendering
    setTimeout(() => {
      if (infoWindowRef.current) {
        infoWindowRef.current.setContent(createInfoWindowContent(event));
        infoWindowRef.current.open(googleMapRef.current, marker);
        console.log(`✅ InfoWindow opened for event ${event.id || event.ticket}`);
      }
    }, 100);
  };

  // Calculate bounds from events
  const calculateBounds = (events: MapEvent[]) => {
    if (events.length === 0) return null;
    
    const validEvents = events.filter(e => 
      e.position?.lat && e.position?.lng && 
      !isNaN(e.position.lat) && !isNaN(e.position.lng)
    );
    
    if (validEvents.length === 0) return null;
    
    const bounds = new window.google.maps.LatLngBounds();
    validEvents.forEach(event => {
      bounds.extend(event.position);
    });
    
    return bounds;
  };

  // Create and manage markers
  useEffect(() => {
    if (!googleMapRef.current || !window.google || events.length === 0) return;

    console.log(`🎯 Creating markers for ${events.length} events`);

    // Listen for custom event selection
    const handleEventSelect = (e: any) => {
      if (onEventSelect) {
        onEventSelect(e.detail);
      }
    };
    window.addEventListener('eventSelect', handleEventSelect);

    // Clear existing markers and clusterer
    markersRef.current.forEach(marker => {
      if (marker && marker.map) {
        marker.map = null;
      }
    });
    markersRef.current.clear();

    if (clustererRef.current) {
      clustererRef.current.clearMarkers();
    }

    // Filter valid events
    const validEvents = events.filter(event => {
      const lat = event.position?.lat;
      const lng = event.position?.lng;
      const isValid = lat && lng && !isNaN(lat) && !isNaN(lng) && 
                     Math.abs(lat) > 0.001 && Math.abs(lng) > 0.001;
      
      if (!isValid) {
        console.log(`❌ Invalid coordinates for event ${event.id}:`, event.position);
      }
      
      return isValid;
    });

    console.log(`📍 Creating ${validEvents.length} valid event markers`);

    // Check if we should use clustering
    const useCluster = validEvents.length > maxMarkersBeforeClustering;
    console.log(`Using clustering: ${useCluster} (${validEvents.length} events, threshold: ${maxMarkersBeforeClustering})`);

    const markers: any[] = [];

    // Create markers for each event
    validEvents.forEach((event, index) => {
      try {
        console.log(`Creating event marker ${index + 1}/${validEvents.length} for ${event.id || event.ticket}`);

        // Try AdvancedMarkerElement first
        let marker;
        
        if (window.google.maps.marker?.AdvancedMarkerElement) {
          marker = new window.google.maps.marker.AdvancedMarkerElement({
            map: useCluster ? null : googleMapRef.current, // Add to map directly if not clustering
            position: event.position,
            content: createEventMarkerIcon(event),
            title: `${event.type_name || event.type} - ${event.name}`,
            gmpClickable: true,
          });

          marker.addListener('click', (clickEvent: any) => {
            handleMarkerClick(event, marker, clickEvent);
          });
        } else {
          // Fallback to regular Marker
          const eventTypeColors = {
            'stop': 'blue',
            'hardBrake': 'red',
            'acceleration': 'yellow', 
            'speeding': 'orange',
            'turn': 'purple',
            'collision': 'red',
            'custom': 'green'
          };

          const color = eventTypeColors[event.type] || 'blue';
          
          marker = new window.google.maps.Marker({
            map: useCluster ? null : googleMapRef.current,
            position: event.position,
            title: `${event.type_name || event.type} - ${event.name}`,
            icon: {
              url: `https://maps.google.com/mapfiles/ms/icons/${color}-dot.png`,
              scaledSize: new window.google.maps.Size(32, 32),
            }
          });

          marker.addListener('click', (clickEvent: any) => {
            if (clickEvent?.stop) clickEvent.stop();
            handleMarkerClick(event, marker, clickEvent);
          });
        }

        markersRef.current.set(event.id || event.ticket || index.toString(), marker);
        markers.push(marker);

        console.log(`✅ Event marker created for ${event.id || event.ticket}`);
      } catch (err) {
        console.error(`❌ Error creating marker for event ${event.id || event.ticket}:`, err);
      }
    });

    // Set up clustering if needed and MarkerClusterer is available
    if (useCluster && (window as any).markerClusterer && markers.length > 0) {
      try {
        const MarkerClusterer = (window as any).markerClusterer.MarkerClusterer;
        const SuperClusterAlgorithm = (window as any).markerClusterer.SuperClusterAlgorithm;
        
        clustererRef.current = new MarkerClusterer({
          map: googleMapRef.current,
          markers: markers,
          algorithm: new SuperClusterAlgorithm({
            radius: 50,
            maxZoom: 15,
          }),
        });
        console.log('✅ Event markers clustered');
      } catch (clusterError) {
        console.warn('⚠️ Clustering failed, adding markers directly:', clusterError);
        markers.forEach(marker => {
          marker.map = googleMapRef.current;
        });
      }
    } else if (!useCluster && markers.length > 0) {
      // Add markers directly to map
      markers.forEach(marker => {
        if (!marker.map) {
          marker.map = googleMapRef.current;
        }
      });
    }

    // Fit bounds to show all events
    if (validEvents.length > 0 && !selectedEventId) {
      const bounds = calculateBounds(validEvents);
      if (bounds) {
        googleMapRef.current.fitBounds(bounds, { padding: 50 });
      }
    }

    return () => {
      window.removeEventListener('eventSelect', handleEventSelect);
    };
  }, [events, maxMarkersBeforeClustering, isLoaded]);

  // Handle external selection
  useEffect(() => {
    if (!googleMapRef.current || !selectedEventId) return;

    const event = events.find(e => (e.id || e.ticket) === selectedEventId);
    if (event) {
      const marker = markersRef.current.get(selectedEventId);
      if (marker) {
        handleMarkerClick(event, marker);
        googleMapRef.current.panTo(event.position);
        googleMapRef.current.setZoom(15);
      }
    }
  }, [selectedEventId, events]);

  // Fit bounds to show all events
  const fitBounds = () => {
    if (!googleMapRef.current || events.length === 0) return;

    const bounds = calculateBounds(events);
    if (bounds) {
      googleMapRef.current.fitBounds(bounds, { padding: 50 });
    }
  };

  // Error state
  if (error) {
    return (
      <div className={`flex items-center justify-center h-96 bg-red-50 border-2 border-red-300 rounded ${className}`}>
        <div className="text-center p-6">
          <div className="text-red-600 text-xl font-bold mb-2">❌ Google Maps Error</div>
          <div className="text-red-500 mb-2">{error}</div>
          {!apiKey && (
            <div className="text-gray-600 text-sm">
              <div>Please add your Google Maps API key to your .env file:</div>
              <div className="font-mono bg-gray-100 p-2 mt-2 rounded">
                VITE_APP_GOOGLE_MAPS_API_KEY=your_api_key_here
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Loading state
  if (!isLoaded) {
    return (
      <div className={`flex items-center justify-center h-96 bg-blue-50 border-2 border-blue-300 rounded ${className}`}>
        <div className="text-center p-6">
          <Spinner color="primary" size="large" />
          <div className="text-blue-600 text-lg font-semibold mt-4">Loading Events Map...</div>
          <div className="text-blue-500 text-sm mt-2">Initializing Google Maps with clustering</div>
        </div>
      </div>
    );
  }

  // No events state
  if (events.length === 0) {
    return (
      <div className={`flex items-center justify-center h-96 bg-gray-100 p-4 rounded-lg ${className}`}>
        <div className="text-center">
          <div className="text-gray-400 text-4xl mb-4">📍</div>
          <p className="text-gray-500">No events to display</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative h-full ${className}`}>
      <div ref={mapRef} className="w-full h-full rounded-lg overflow-hidden" />
      
      {/* Map controls */}
      <div className="absolute top-12 left-3 bg-white rounded-lg shadow-lg p-2">
        <button
          onClick={fitBounds}
          className="px-3 py-2 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          title="Fit all events in view"
        >
          🎯 Fit All Events
        </button>
      </div>
      
      {/* Event count indicator for large datasets */}
      {events.length > 1000 && (
        <div className="absolute top-4 right-4 bg-white px-3 py-1 rounded-full shadow-md text-sm">
          📊 {events.length.toLocaleString()} events
        </div>
      )}

      {/* Event type legend */}
      <div className="absolute bottom-4 right-4 bg-white rounded-lg shadow-lg p-3 max-w-xs">
        <div className="space-y-2">
          <div className="text-sm font-semibold text-gray-700 border-b pb-2">
            📍 Event Types
          </div>
          
          <div className="grid grid-cols-2 gap-2 text-xs">
            {[
              { type: 'stop', icon: '⏹️', label: 'Stop' },
              { type: 'hardBrake', icon: '🛑', label: 'Hard Brake' },
              { type: 'acceleration', icon: '⚡', label: 'Acceleration' },
              { type: 'speeding', icon: '💨', label: 'Speeding' },
              { type: 'turn', icon: '↻', label: 'Turn' },
              { type: 'collision', icon: '💥', label: 'Collision' }
            ].map(({ type, icon, label }) => {
              const count = events.filter(e => e.type === type).length;
              return count > 0 ? (
                <div key={type} className="flex items-center gap-2">
                  <span>{icon}</span>
                  <span>{label} ({count})</span>
                </div>
              ) : null;
            })}
          </div>
          
          <div className="border-t pt-2 text-xs">
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                <span>Severe ({events.filter(e => (e.severity_level || 0) >= 7).length})</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                <span>Moderate ({events.filter(e => (e.severity_level || 0) >= 4 && (e.severity_level || 0) < 7).length})</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
                <span>Minor ({events.filter(e => (e.severity_level || 0) < 4).length})</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EventsMap;