// src/components/configuration/ConfigField.tsx
import React from 'react';

interface ConfigFieldProps {
  label: string;
  description?: string;
  id: string;
  type: 'text' | 'number' | 'boolean' | 'select';
  value: any;
  options?: Array<{ value: any; label: string }>;
  onChange: (value: any) => void;
  isModified?: boolean;
}

const ConfigField: React.FC<ConfigFieldProps> = ({
  label,
  description,
  id,
  type,
  value,
  options,
  onChange,
  isModified = false,
}) => {
  const renderField = () => {
    switch (type) {
      case 'boolean':
        return (
          <div className="flex items-center">
            <input
              id={id}
              type="checkbox"
              checked={value}
              onChange={(e) => onChange(e.target.checked)}
              className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
            />
          </div>
        );
      case 'number':
        return (
          <input
            id={id}
            type="number"
            value={value}
            onChange={(e) => onChange(Number(e.target.value))}
            className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
              isModified ? 'border-yellow-500 bg-yellow-50' : 'border-gray-300'
            }`}
          />
        );
      case 'select':
        return (
          <select
            id={id}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
              isModified ? 'border-yellow-500 bg-yellow-50' : 'border-gray-300'
            }`}
          >
            {options?.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );
      case 'text':
      default:
        return (
          <input
            id={id}
            type="text"
            value={value}
            onChange={(e) => onChange(e.target.value)}
            className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
              isModified ? 'border-yellow-500 bg-yellow-50' : 'border-gray-300'
            }`}
          />
        );
    }
  };

  return (
    <div className="mb-4">
      <div className="flex items-center">
        <label
          htmlFor={id}
          className={`block text-sm font-medium ${
            isModified ? 'text-yellow-700' : 'text-gray-700'
          }`}
        >
          {label}
        </label>
        {isModified && (
          <span className="ml-2 text-xs bg-yellow-100 text-yellow-800 px-2 py-0.5 rounded-full">
            Modified
          </span>
        )}
      </div>
      {description && (
        <p className="mt-1 text-xs text-gray-500">{description}</p>
      )}
      <div className="mt-1">{renderField()}</div>
    </div>
  );
};

export default ConfigField;