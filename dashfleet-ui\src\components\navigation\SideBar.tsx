import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  Home, 
  Truck, 
  Users, 
  AlertTriangle, 
  Map, 
  Settings, 
  FileText, 
  BarChart2, 
  HelpCircle, 
  X,
  LogOut,
  Cog,
  Smartphone,
  Route,
  Navigation,
  CreditCard
} from 'lucide-react';

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
  user: {
    name: string;
    email: string;
    avatar?: string;
    role?: string;
  };
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen, onClose, user }) => {
  const location = useLocation();
  
  // Navigation items
  const navigationItems = [
    { name: 'Dashboard', path: '/', icon: <Home size={20} /> },
    { name: 'Map', path: '/map', icon: <Map size={20} /> },
    { name: 'Devices', path: '/devices', icon: <Smartphone size={20} /> }, // New Devices menu item
    { name: 'Vehicles', path: '/vehicles', icon: <Truck size={20} /> },
    { name: 'Drivers', path: '/drivers', icon: <Users size={20} /> },
    { name: 'Events', path: '/events', icon: <AlertTriangle size={20} /> },
    {
      name: 'Trips List',
      icon: <Route size={20} />,     
      path: '/trips',
    },
    {
      name: 'Trips Report',
      icon: <FileText size={20} />,     
      path: '/report',

    },
    {
      name: 'Payment',
      icon: <CreditCard size={20} />,     
      path: '/payment',

    },
    { name: 'Analytics', path: '/analytics', icon: <BarChart2 size={20} /> },
    { name: 'Configuration', path: '/configuration', icon: <Cog size={20} /> },
  ];

  // Utility/secondary items
  const utilityItems = [
    { name: 'Settings', path: '/settings', icon: <Settings size={20} /> },
    { name: 'Help', path: '/help', icon: <HelpCircle size={20} /> },
  ];

  // Check if a path is active
  const isActivePath = (path: string): boolean => {
    if (path === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(path);
  };

  // Render navigation item
  const renderNavItem = (item: { name: string; path: string; icon: JSX.Element }, index: number) => {
    const isActive = isActivePath(item.path);
    
    return (
      <li key={index}>
        <Link
          to={item.path}
          className={`flex items-center px-4 py-3 text-sm font-medium rounded-md transition-colors ${
            isActive
              ? 'bg-blue-50 text-blue-700'
              : 'text-gray-700 hover:text-blue-700 hover:bg-blue-50'
          }`}
        >
          <span className={`mr-3 ${isActive ? 'text-blue-500' : 'text-gray-500'}`}>
            {item.icon}
          </span>
          {item.name}
        </Link>
      </li>
    );
  };

  return (
    <>
      {/* Mobile backdrop */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={onClose}
        ></div>
      )}

      {/* Sidebar */}
      <div
        className={`fixed inset-y-0 left-0 z-50 w-64 bg-white border-r transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:z-auto ${
          isOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
      >
        <div className="flex flex-col h-full">
          {/* Sidebar header with logo and close button */}
          <div className="flex items-center justify-between h-16 px-4 border-b">
            <Link to="/" className="flex items-center">
              <img 
                src="/logo.svg" 
                alt="DashFleet Logo" 
                className="h-8 w-auto" 
                onError={(e) => {
                  e.currentTarget.src = 'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32"><rect width="32" height="32" fill="%234a86e8"/><text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle" font-family="Arial" font-size="16" fill="white">DF</text></svg>';
                }}
              />
              <span className="ml-2 text-lg font-bold text-gray-900">DashFleet</span>
            </Link>
            <button
              className="p-1 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 lg:hidden"
              onClick={onClose}
            >
              <X size={20} />
            </button>
          </div>

          {/* User info */}
          <div className="p-4 border-b">
            <div className="flex items-center">
              <div className="flex-shrink-0 h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
                {user.avatar ? (
                  <img 
                    src={user.avatar} 
                    alt={user.name} 
                    className="h-10 w-10 rounded-full"
                  />
                ) : (
                  <span className="text-blue-600 font-medium text-lg">
                    {user.name ? user.name.charAt(0).toUpperCase() : 'U'}
                  </span>
                )}
              </div>
              <div className="ml-3 overflow-hidden">
                <p className="text-sm font-medium text-gray-900 truncate">{user.name}</p>
                <p className="text-xs text-gray-500 truncate">{user.email}</p>
                {user.role && (
                  <p className="text-xs text-gray-500">{user.role}</p>
                )}
              </div>
            </div>
          </div>

          {/* Navigation */}
          <div className="flex-1 overflow-y-auto p-3">
            <nav className="space-y-1">
              <ul>
                {navigationItems.map(renderNavItem)}
              </ul>
            </nav>
            
            {/* Utility navigation */}
            <nav className="mt-8">
              <h3 className="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                Settings
              </h3>
              <ul className="mt-2 space-y-1">
                {utilityItems.map(renderNavItem)}
              </ul>
            </nav>
          </div>

          {/* Sidebar footer */}
          <div className="p-4 border-t">Report
            <button
              className="flex items-center justify-center w-full px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
              onClick={() => {
                // Handle logout - this would typically dispatch a logout action
                console.log('Logout clicked');
              }}
            >
              <LogOut size={16} className="mr-2" />
              Sign Out
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;