import React, { useState, useRef, useEffect } from 'react';

interface TooltipProps {
  content: string | React.ReactNode;
  children: React.ReactNode;
  position?: 'top' | 'bottom' | 'left' | 'right' | 'auto';
  delay?: number;
  className?: string;
  maxWidth?: string;
  disabled?: boolean;
}

export const Tooltip: React.FC<TooltipProps> = ({
  content,
  children,
  position = 'auto',
  delay = 300,
  className = '',
  maxWidth = '200px',
  disabled = false,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [actualPosition, setActualPosition] = useState(position);
  const [timeoutId, setTimeoutId] = useState<NodeJS.Timeout | null>(null);
  const triggerRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [timeoutId]);

  const calculatePosition = () => {
    if (!triggerRef.current || !tooltipRef.current || position !== 'auto') {
      setActualPosition(position);
      return;
    }

    const triggerRect = triggerRef.current.getBoundingClientRect();
    const tooltipRect = tooltipRef.current.getBoundingClientRect();
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight,
    };

    // Check available space in each direction
    const spaceTop = triggerRect.top;
    const spaceBottom = viewport.height - triggerRect.bottom;
    const spaceLeft = triggerRect.left;
    const spaceRight = viewport.width - triggerRect.right;

    // Determine best position based on available space
    if (spaceBottom >= tooltipRect.height + 10) {
      setActualPosition('bottom');
    } else if (spaceTop >= tooltipRect.height + 10) {
      setActualPosition('top');
    } else if (spaceRight >= tooltipRect.width + 10) {
      setActualPosition('right');
    } else if (spaceLeft >= tooltipRect.width + 10) {
      setActualPosition('left');
    } else {
      // If no space is adequate, default to bottom
      setActualPosition('bottom');
    }
  };

  const handleMouseEnter = () => {
    if (disabled) return;
    
    const id = setTimeout(() => {
      setIsVisible(true);
      setTimeout(calculatePosition, 0);
    }, delay);
    setTimeoutId(id);
  };

  const handleMouseLeave = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      setTimeoutId(null);
    }
    setIsVisible(false);
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Escape') {
      setIsVisible(false);
    }
  };

  const getTooltipClasses = () => {
    const baseClasses = `
      absolute z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-md shadow-lg
      transition-opacity duration-200 pointer-events-none whitespace-nowrap
      ${isVisible ? 'opacity-100' : 'opacity-0'}
    `;

    const positionClasses = {
      top: 'bottom-full left-1/2 transform -translate-x-1/2 -translate-y-2',
      bottom: 'top-full left-1/2 transform -translate-x-1/2 translate-y-2',
      left: 'right-full top-1/2 transform -translate-y-1/2 -translate-x-2',
      right: 'left-full top-1/2 transform -translate-y-1/2 translate-x-2',
    };

    return `${baseClasses} ${positionClasses[actualPosition]} ${className}`.trim();
  };

  const getArrowClasses = () => {
    const arrowClasses = {
      top: 'absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900',
      bottom: 'absolute bottom-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-b-gray-900',
      left: 'absolute left-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-l-gray-900',
      right: 'absolute right-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-r-gray-900',
    };

    return arrowClasses[actualPosition];
  };

  return (
    <div className="relative inline-block">
      <div
        ref={triggerRef}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onKeyDown={handleKeyDown}
        className="cursor-help"
        tabIndex={0}
        role="button"
        aria-describedby={isVisible ? 'tooltip' : undefined}
      >
        {children}
      </div>
      
      {!disabled && (
        <div
          ref={tooltipRef}
          id="tooltip"
          role="tooltip"
          className={getTooltipClasses()}
          style={{ 
            maxWidth,
            visibility: isVisible ? 'visible' : 'hidden'
          }}
        >
          <div className="relative">
            {content}
            <div className={getArrowClasses()}></div>
          </div>
        </div>
      )}
    </div>
  );
};

// Alternative simpler tooltip for specific use cases
export const SimpleTooltip: React.FC<{
  text: string;
  children: React.ReactNode;
  className?: string;
}> = ({ text, children, className = '' }) => {
  const [isVisible, setIsVisible] = useState(false);

  return (
    <div className="relative inline-block group">
      <div
        onMouseEnter={() => setIsVisible(true)}
        onMouseLeave={() => setIsVisible(false)}
        className="cursor-help"
      >
        {children}
      </div>
      
      {isVisible && (
        <div className={`
          absolute z-50 px-2 py-1 text-xs text-white bg-gray-800 rounded
          shadow-lg whitespace-nowrap transform -translate-x-1/2 -translate-y-full
          left-1/2 bottom-full mb-2 transition-opacity duration-200
          ${className}
        `}>
          {text}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-2 border-transparent border-t-gray-800"></div>
        </div>
      )}
    </div>
  );
};

// Question mark icon component specifically for help tooltips
export const HelpTooltip: React.FC<{
  content: string | React.ReactNode;
  size?: 'sm' | 'md' | 'lg';
}> = ({ content, size = 'md' }) => {
  const sizeClasses = {
    sm: 'w-4 h-4 text-xs',
    md: 'w-5 h-5 text-sm',
    lg: 'w-6 h-6 text-base',
  };

  return (
    <Tooltip content={content} position="auto">
      <div className={`
        ${sizeClasses[size]} rounded-full bg-gray-300 hover:bg-gray-400
        flex items-center justify-center transition-colors duration-200
        cursor-help focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50
      `}>
        <span className="font-medium text-gray-600">?</span>
      </div>
    </Tooltip>
  );
};

export default Tooltip;