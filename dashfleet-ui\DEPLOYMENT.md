# DashFleet UI - Vercel Deployment Guide

## Prerequisites

1. **Vercel Account**: Sign up at [vercel.com](https://vercel.com)
2. **Git Repository**: Your code should be in a Git repository (GitHub, GitLab, or Bitbucket)
3. **Environment Variables**: Set up your environment variables

## Deployment Steps

### Method 1: Deploy via Vercel Dashboard (Recommended)

1. **Connect Repository**:
   - Go to [vercel.com/dashboard](https://vercel.com/dashboard)
   - Click "New Project"
   - Import your Git repository

2. **Configure Project**:
   - Framework Preset: **Vite**
   - Root Directory: `dashfleet-ui` (if your project is in a subdirectory)
   - Build Command: `npm run build`
   - Output Directory: `dist`
   - Install Command: `npm install`

3. **Set Environment Variables**:
   - Go to Project Settings → Environment Variables
   - Add the following variables:
     ```
     VITE_API_BASE_URL=https://api.visionmaxfleet.com
     VITE_GOOGLE_MAPS_API_KEY=your_google_maps_api_key
     VITE_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
     VITE_WEBSOCKET_URL=wss://api.visionmaxfleet.com/ws
     VITE_MQTT_BROKER_URL=wss://mqtt.visionmaxfleet.com:8084/mqtt
     VITE_APP_ENVIRONMENT=production
     ```

4. **Deploy**:
   - Click "Deploy"
   - Wait for the build to complete

### Method 2: Deploy via Vercel CLI

1. **Install Vercel CLI**:
   ```bash
   npm install -g vercel
   ```

2. **Login to Vercel**:
   ```bash
   vercel login
   ```

3. **Deploy**:
   ```bash
   # For preview deployment
   npm run deploy:preview
   
   # For production deployment
   npm run deploy
   ```

## Environment Variables Setup

### Required Variables:
- `VITE_API_BASE_URL`: Your backend API URL
- `VITE_GOOGLE_MAPS_API_KEY`: Google Maps API key for map features
- `VITE_STRIPE_PUBLISHABLE_KEY`: Stripe publishable key for payments

### Optional Variables:
- `VITE_WEBSOCKET_URL`: WebSocket URL for real-time features
- `VITE_MQTT_BROKER_URL`: MQTT broker URL for IoT device communication
- `VITE_APP_ENVIRONMENT`: Application environment (production/staging)

## Domain Configuration

1. **Custom Domain**:
   - Go to Project Settings → Domains
   - Add your custom domain
   - Configure DNS records as instructed

2. **SSL Certificate**:
   - Vercel automatically provides SSL certificates
   - No additional configuration needed

## Performance Optimizations

The project is configured with:
- **Code Splitting**: Automatic chunk splitting for better loading
- **Asset Optimization**: Images and static assets are optimized
- **Caching**: Proper cache headers for static assets
- **Compression**: Gzip compression enabled

## Monitoring and Analytics

1. **Vercel Analytics**:
   - Enable in Project Settings → Analytics
   - Monitor page views and performance

2. **Error Tracking**:
   - Consider integrating Sentry or similar service
   - Add error boundary components

## Troubleshooting

### Common Issues:

1. **Build Failures**:
   - Check TypeScript errors: `npm run type-check`
   - Verify all dependencies are installed
   - Check environment variables

2. **Routing Issues**:
   - Ensure `vercel.json` rewrites are configured
   - Check React Router configuration

3. **API Connection Issues**:
   - Verify CORS settings on your backend
   - Check environment variable values
   - Ensure API endpoints are accessible

### Build Commands:
```bash
# Local development
npm run dev

# Type checking
npm run type-check

# Build for production
npm run build

# Preview production build locally
npm run preview
```

## Continuous Deployment

Vercel automatically deploys:
- **Production**: When you push to your main/master branch
- **Preview**: When you create pull requests or push to other branches

## Support

For deployment issues:
1. Check Vercel documentation: [vercel.com/docs](https://vercel.com/docs)
2. Review build logs in Vercel dashboard
3. Check this project's GitHub issues
