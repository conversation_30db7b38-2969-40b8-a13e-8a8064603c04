import React, { useEffect, useRef, useState, useCallback } from 'react';
import { X, Camera, Volume2, VolumeX, AlertTriangle } from 'lucide-react'; // Added AlertTriangle

// Define structure for credentials prop for clarity
interface LiveViewCredentials {
    key: string;
    secret: string;
    token?: string; // Optional or potentially unused
    topics?: { // Optional or potentially unused
        credential?: string;
        device_report?: string;
    };
    client_id?: string[]; // Optional or potentially unused
}

interface LiveViewModalProps {
    deviceId: string; // For display or identification
    cid: string; // Needed for WebSocket URL
    requestId: string; // Needed for WebSocket URL
    credentials: LiveViewCredentials; // Use the defined interface
    onError: (error: Error) => void; // Callback for reporting errors
    onClose: () => void; // Callback for closing the modal
}

const MAX_CONNECTION_ATTEMPTS = 3;
const CONNECTION_TIMEOUT_MS = 15000; // 15 seconds
const SESSION_DURATION_SECONDS = 600; // 10 minutes

const LiveViewModal: React.FC<LiveViewModalProps> = ({
    deviceId,
    cid,
    requestId,
    credentials,
    onError,
    onClose
}) => {
    const videoRef = useRef<HTMLVideoElement>(null);
    const [isConnecting, setIsConnecting] = useState(true);
    const [isMuted, setIsMuted] = useState(true);
    const [connectionError, setConnectionError] = useState<string | null>(null);
    const [remainingTime, setRemainingTime] = useState<string | null>(null); // Start as null

    const webSocketRef = useRef<WebSocket | null>(null);
    const peerConnectionRef = useRef<RTCPeerConnection | null>(null);
    const connectionAttemptsRef = useRef(0);
    const isConnectedRef = useRef(false); // Tracks WebSocket connection state
    const isComponentMountedRef = useRef(true); // Track mount status for async operations
    const initializingRef = useRef(false); // Prevent concurrent initializations
    const sessionTimerIntervalRef = useRef<NodeJS.Timeout | null>(null); // Ref for timer interval
    const connectionTimeoutRef = useRef<NodeJS.Timeout | null>(null); // Ref for WS connection timeout

    // --- Cleanup Function ---
    // Defined separately for clarity and reuse
    const cleanupConnections = useCallback(() => {
        console.log('LiveViewModal: Running cleanupConnections...');

         // Clear any pending timeouts/intervals
        if (connectionTimeoutRef.current) {
            clearTimeout(connectionTimeoutRef.current);
            connectionTimeoutRef.current = null;
        }
        if (sessionTimerIntervalRef.current) {
            clearInterval(sessionTimerIntervalRef.current);
            sessionTimerIntervalRef.current = null;
        }

        // Close Peer Connection
        if (peerConnectionRef.current) {
            console.log('LiveViewModal: Closing RTCPeerConnection');
            try {
                peerConnectionRef.current.close();
            } catch (e) { console.error("Error closing PeerConnection:", e); }
            peerConnectionRef.current = null;
        }

        // Close WebSocket
        if (webSocketRef.current) {
            console.log(`LiveViewModal: Closing WebSocket (readyState: ${webSocketRef.current.readyState})`);
            if (webSocketRef.current.readyState === WebSocket.OPEN || webSocketRef.current.readyState === WebSocket.CONNECTING) {
                try {
                    webSocketRef.current.close(1000, "Client closing connection"); // Normal closure
                } catch (e) { console.error("Error closing WebSocket:", e); }
            }
            webSocketRef.current = null;
        }

        // Clean up Video Element Source
        if (videoRef.current && videoRef.current.srcObject) {
            console.log('LiveViewModal: Cleaning up video tracks');
            try {
                const stream = videoRef.current.srcObject as MediaStream;
                stream.getTracks().forEach(track => track.stop());
                videoRef.current.srcObject = null;
            } catch (e) { console.error("Error cleaning video source:", e); }
        }

        // Reset flags
        isConnectedRef.current = false;
        initializingRef.current = false;

    }, []); // No dependencies needed for cleanup logic itself


    // --- WebRTC Initialization ---
    const initializeWebRTC = useCallback(async () => {
        // Prevent duplicate initialization or excessive attempts
        if (initializingRef.current || isConnectedRef.current || connectionAttemptsRef.current >= MAX_CONNECTION_ATTEMPTS) {
            if (connectionAttemptsRef.current >= MAX_CONNECTION_ATTEMPTS) {
                console.warn(`Max connection attempts (${MAX_CONNECTION_ATTEMPTS}) reached.`);
                if (isComponentMountedRef.current) {
                   setConnectionError(`Failed to connect after ${MAX_CONNECTION_ATTEMPTS} attempts.`);
                   setIsConnecting(false);
                }
            } else {
                 console.log('Connection attempt skipped (already initializing or connected).');
            }
            return;
        }

        // Check for essential credentials before proceeding
        if (!credentials || !credentials.key || !credentials.secret || !cid || !requestId) {
             console.error("LiveViewModal: Missing required props (credentials, cid, requestId). Cannot connect.");
             if (isComponentMountedRef.current) {
                 setConnectionError("Configuration error: Missing connection details.");
                 setIsConnecting(false);
                 onError(new Error("LiveView configuration error"));
             }
             return;
        }

        initializingRef.current = true;
        connectionAttemptsRef.current += 1;
        console.log(`LiveViewModal: Connection attempt ${connectionAttemptsRef.current}/${MAX_CONNECTION_ATTEMPTS}...`);

        if (!isComponentMountedRef.current) {
            console.log("InitializeWebRTC called after unmount, aborting.");
            initializingRef.current = false;
            return;
        }

        // Reset previous error and set connecting state
        setConnectionError(null);
        setIsConnecting(true);
        cleanupConnections(); // Clean up any previous stale connections before starting new one


        try {
            const wsUrl = `wss://dev-ws-connect.visionmaxfleet.com/signaling/${cid}?secret=${credentials.secret}&key=${credentials.key}&request_id=${requestId}`;
            console.log('LiveViewModal: Creating WebSocket connection to:', wsUrl);
            const ws = new WebSocket(wsUrl);

            webSocketRef.current = ws;

            // Connection Timeout
            connectionTimeoutRef.current = setTimeout(() => {
                if (ws.readyState !== WebSocket.OPEN && isComponentMountedRef.current) {
                    console.error(`WebSocket connection timeout after ${CONNECTION_TIMEOUT_MS}ms.`);
                    ws.close(1001, "Connection Timeout"); // Going away
                    if (!connectionError) { // Avoid overwriting more specific errors
                         setConnectionError('Connection timed out. Please check device status and network.');
                         onError(new Error('WebSocket connection timeout'));
                    }
                    setIsConnecting(false);
                    initializingRef.current = false;
                     // Optionally trigger retry here or let user do it
                }
            }, CONNECTION_TIMEOUT_MS);

            ws.onopen = () => {
                if (!isComponentMountedRef.current) return ws.close(); // Close if unmounted during connection
                console.log('LiveViewModal: WebSocket connection established.');
                clearTimeout(connectionTimeoutRef.current!);
                connectionTimeoutRef.current = null;
                isConnectedRef.current = true; // Mark WS as connected

                // Send initial message to request video stream
                const message = {
                    type: 'request_stream',
                    device_id: deviceId, // Use the passed deviceId prop
                    stream_type: 'live' // Or make this configurable if needed
                };
                console.log('LiveViewModal: Sending request_stream:', message);
                ws.send(JSON.stringify(message));
                 // Note: isConnecting remains true until video track is received
            };

            ws.onmessage = async (event) => {
                if (!isComponentMountedRef.current) return; // Ignore messages if unmounted

                try {
                    const message = JSON.parse(event.data);
                    console.log('LiveViewModal: Received WS message type:', message.type);

                    switch (message.type) {
                        case 'offer':
                            console.log('LiveViewModal: Received SDP offer. Creating PeerConnection.');
                             // Create RTCPeerConnection if it doesn't exist or reuse? Usually create new.
                            if (peerConnectionRef.current) {
                                console.warn("Existing PeerConnection found, closing before creating new one.");
                                peerConnectionRef.current.close();
                            }

                            const peerConnection = new RTCPeerConnection({
                                iceServers: [
                                    { urls: 'stun:stun.l.google.com:19302' },
                                    // Add TURN servers here if needed for production
                                    // { urls: 'turn:your.turn.server:3478', username: 'user', credential: 'password' }
                                ]
                            });
                            peerConnectionRef.current = peerConnection;

                            // Handle incoming tracks
                            peerConnection.ontrack = (event) => {
                                if (!isComponentMountedRef.current || !videoRef.current) return;
                                console.log('LiveViewModal: Received remote track:', event.track.kind);
                                if (event.track.kind === 'video' && event.streams[0]) {
                                    videoRef.current.srcObject = event.streams[0];
                                    console.log('LiveViewModal: Video stream attached.');
                                     if (isConnecting) { // Only update state if it was connecting
                                        setIsConnecting(false); // Successfully connected!
                                        startSessionTimer(); // Start countdown now
                                     }
                                }
                                // Handle audio track if needed (already part of the stream)
                            };

                            // Handle ICE candidates
                            peerConnection.onicecandidate = (event) => {
                                // Send candidate via WebSocket if websocket is open
                                if (event.candidate && webSocketRef.current && webSocketRef.current.readyState === WebSocket.OPEN) {
                                    console.log('LiveViewModal: Sending ICE candidate...');
                                    webSocketRef.current.send(JSON.stringify({
                                        type: 'ice_candidate',
                                        candidate: event.candidate
                                    }));
                                }
                            };

                             peerConnection.oniceconnectionstatechange = () => {
                                if (!peerConnectionRef.current) return;
                                console.log(`LiveViewModal: ICE connection state: ${peerConnectionRef.current.iceConnectionState}`);
                                if (['failed', 'disconnected', 'closed'].includes(peerConnectionRef.current.iceConnectionState)) {
                                     if (isComponentMountedRef.current && !connectionError) {
                                          console.error("ICE connection failed or disconnected.");
                                          setConnectionError("Video connection lost or failed.");
                                          setIsConnecting(false); // No longer actively connecting
                                          onError(new Error("ICE connection state issue: " + peerConnectionRef.current.iceConnectionState));
                                          cleanupConnections(); // Clean up on ICE failure
                                     }
                                }
                            };

                            // Set remote description (the offer)
                            await peerConnection.setRemoteDescription(new RTCSessionDescription(message.sdp));
                            console.log('LiveViewModal: Remote description (offer) set.');

                            // Create answer
                            const answer = await peerConnection.createAnswer();
                            await peerConnection.setLocalDescription(answer);
                            console.log('LiveViewModal: Local description (answer) set.');

                            // Send answer back via WebSocket
                             if (webSocketRef.current && webSocketRef.current.readyState === WebSocket.OPEN) {
                                console.log('LiveViewModal: Sending answer...');
                                webSocketRef.current.send(JSON.stringify({
                                    type: 'answer',
                                    sdp: peerConnection.localDescription
                                }));
                             } else {
                                 console.error("WebSocket closed before answer could be sent.");
                                 throw new Error("WebSocket closed unexpectedly during negotiation");
                             }
                            break; // End case 'offer'

                        case 'ice_candidate':
                            if (peerConnectionRef.current && message.candidate) {
                                console.log('LiveViewModal: Adding received ICE candidate...');
                                try {
                                   await peerConnectionRef.current.addIceCandidate(new RTCIceCandidate(message.candidate));
                                } catch (e) { console.error("Error adding received ICE candidate:", e); }
                            }
                            break; // End case 'ice_candidate'

                        case 'error': // Handle specific errors from signaling server
                            console.error('LiveViewModal: Received error message from server:', message.message);
                            if (isComponentMountedRef.current) {
                                const errorMsg = message.message || 'Unknown error from server';
                                setConnectionError(errorMsg);
                                setIsConnecting(false);
                                onError(new Error(errorMsg));
                                cleanupConnections(); // Clean up on server error
                            }
                            break; // End case 'error'

                         case 'stream_unavailable': // Example: Handle specific server message
                            console.warn('LiveViewModal: Server indicated stream is unavailable.');
                             if (isComponentMountedRef.current) {
                                 setConnectionError('Device stream is currently unavailable.');
                                 setIsConnecting(false);
                                 onError(new Error('Stream unavailable'));
                                 cleanupConnections();
                            }
                            break;

                        default:
                            console.log('LiveViewModal: Received unhandled message type:', message.type);
                    }

                } catch (error: any) {
                    console.error('LiveViewModal: Error processing WebSocket message:', error);
                    if (isComponentMountedRef.current && !connectionError) {
                        setConnectionError('Error processing server message.');
                        setIsConnecting(false);
                        onError(error instanceof Error ? error : new Error('Unknown message processing error'));
                        cleanupConnections(); // Clean up on processing error
                    }
                }
            }; // End ws.onmessage

            ws.onerror = (event) => {
                console.error('LiveViewModal: WebSocket error:', event);
                clearTimeout(connectionTimeoutRef.current!);
                connectionTimeoutRef.current = null;
                 // Check mount status before updating state
                if (!isComponentMountedRef.current) return;

                // Avoid setting generic error if a specific one is already set
                if (!connectionError) {
                    setConnectionError('Failed to connect to the device signaling service.');
                    onError(new Error('WebSocket connection error'));
                }
                setIsConnecting(false); // No longer connecting
                initializingRef.current = false; // Allow retry
                // Don't cleanup here immediately, ws.onclose will handle it
            }; // End ws.onerror

            ws.onclose = (event) => {
                console.log(`LiveViewModal: WebSocket connection closed. Code: ${event.code}, Reason: "${event.reason}"`);
                clearTimeout(connectionTimeoutRef.current!);
                connectionTimeoutRef.current = null;
                isConnectedRef.current = false; // Mark WS as disconnected
                webSocketRef.current = null; // Clear ref

                 if (!isComponentMountedRef.current) {
                     console.log("WebSocket closed after component unmount.");
                     return; // Already cleaned up or being cleaned up
                 }

                // If it closed unexpectedly while trying to connect or while connected without a prior error
                if ((isConnecting || !connectionError) && event.code !== 1000) { // 1000 is normal closure
                     console.warn("WebSocket closed unexpectedly.");
                     setConnectionError(prevError => prevError || `Connection closed unexpectedly (Code: ${event.code}).`);
                     onError(new Error(`WebSocket closed unexpectedly: ${event.code}`));
                }

                setIsConnecting(false); // No longer connecting
                initializingRef.current = false; // Allow retry if desired
                cleanupConnections(); // Ensure everything is cleaned up on close

            }; // End ws.onclose

        } catch (error: any) {
            console.error('LiveViewModal: Error setting up WebRTC/WebSocket:', error);
             if (isComponentMountedRef.current) {
                 setConnectionError('Failed to initialize video connection.');
                 setIsConnecting(false);
                 initializingRef.current = false; // Allow retry
                 onError(error instanceof Error ? error : new Error('Unknown setup error'));
                 cleanupConnections(); // Clean up on initial setup error
             }
        }
    }, [credentials, deviceId, cid, requestId, onError, cleanupConnections]); // Include cleanup in dependencies


    // --- Session Timer ---
    const startSessionTimer = useCallback(() => {
         if (!isComponentMountedRef.current || sessionTimerIntervalRef.current) return; // Don't start if unmounted or timer already running

         console.log('LiveViewModal: Starting session timer.');
         let seconds = SESSION_DURATION_SECONDS;

         const updateTimer = () => {
             const minutes = Math.floor(seconds / 60);
             const remainingSeconds = seconds % 60;
             const timeString = `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
             if (isComponentMountedRef.current) {
                 setRemainingTime(timeString);
             }
         };

         updateTimer(); // Initial display

         sessionTimerIntervalRef.current = setInterval(() => {
             seconds--;
             updateTimer();

             if (seconds <= 0) {
                 console.log('LiveViewModal: Session timer expired.');
                 clearInterval(sessionTimerIntervalRef.current!);
                 sessionTimerIntervalRef.current = null;
                 onClose(); // Trigger modal close via parent
             }
         }, 1000);

     }, [onClose]);


    // --- Effects ---

    // Effect for component mount/unmount and cleanup
    useEffect(() => {
        console.log('LiveViewModal: Mounted');
        isComponentMountedRef.current = true;

        // Start connection attempt on mount
        initializeWebRTC();

        // Cleanup function on unmount
        return () => {
            console.log('LiveViewModal: Unmounting - Performing cleanup.');
            isComponentMountedRef.current = false; // Mark as unmounted
            cleanupConnections(); // Call the main cleanup function
        };
    }, [initializeWebRTC, cleanupConnections,deviceId]); // Depend on initialization and cleanup functions

    // --- Event Handlers ---
    const handleRetry = () => {
        // Reset attempts if user manually retries? Or keep counter? Let's reset for now.
        connectionAttemptsRef.current = 0;
        setConnectionError(null); // Clear error before retry
        setIsConnecting(true); // Show connecting state
        initializeWebRTC(); // Attempt connection again
    };

    const toggleMute = () => {
        if (videoRef.current && videoRef.current.srcObject) {
            const stream = videoRef.current.srcObject as MediaStream;
            const audioTracks = stream.getAudioTracks();
            if (audioTracks.length > 0) {
                const newMutedState = !isMuted;
                audioTracks.forEach(track => {
                    track.enabled = !newMutedState; // Enable track if we are unmuting
                });
                setIsMuted(newMutedState);
                console.log(`LiveViewModal: Audio ${newMutedState ? 'muted' : 'unmuted'}.`);
            } else {
                 console.warn("LiveViewModal: No audio tracks found to toggle mute.");
            }
        }
    };

    // Handle taking a snapshot (Example implementation)
    const handleSnapshot = () => {
        if (videoRef.current && videoRef.current.readyState >= videoRef.current.HAVE_METADATA) {
            const canvas = document.createElement('canvas');
            canvas.width = videoRef.current.videoWidth;
            canvas.height = videoRef.current.videoHeight;
            const ctx = canvas.getContext('2d');
            if (ctx) {
                ctx.drawImage(videoRef.current, 0, 0, canvas.width, canvas.height);
                // Get image data URL (e.g., PNG)
                const dataUrl = canvas.toDataURL('image/png');
                // Create a link to download
                const link = document.createElement('a');
                link.href = dataUrl;
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                link.download = `liveview_${deviceId}_${timestamp}.png`;
                document.body.appendChild(link); // Required for Firefox
                link.click();
                document.body.removeChild(link);
                console.log('LiveViewModal: Snapshot taken.');
            } else {
                console.error("LiveViewModal: Could not get canvas context for snapshot.");
            }
        } else {
             console.warn("LiveViewModal: Video not ready for snapshot.");
        }
    };


    // --- Render ---
    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75 backdrop-blur-sm">
            <div className="bg-white rounded-lg shadow-xl overflow-hidden max-w-4xl w-full mx-4 flex flex-col" style={{ maxHeight: '90vh' }}>
                {/* Header */}
                <div className="flex items-center justify-between px-4 py-3 bg-gray-100 border-b border-gray-300 flex-shrink-0">
                    <div>
                        <h3 className="text-lg font-medium text-gray-900">Device Live View</h3>
                        <p className="text-sm text-gray-600">
                            Device: {deviceId}
                            {remainingTime && <span className="ml-4">Time Left: {remainingTime}</span>}
                        </p>
                    </div>
                    <button
                        title="Close Live View"
                        className="text-gray-500 hover:text-gray-800 focus:outline-none p-1 rounded-full hover:bg-gray-200"
                        onClick={onClose} // Use the onClose prop passed from parent
                    >
                        <X size={20} />
                    </button>
                </div>

                {/* Video container */}
                <div className="relative bg-black flex-grow flex items-center justify-center" style={{ minHeight: '300px' }}> {/* Ensure minimum height */}
                    <video
                        ref={videoRef}
                        className="w-full h-full object-contain" // Contain ensures aspect ratio is kept
                        autoPlay
                        playsInline
                        muted={isMuted} // Controlled by state
                        onLoadedMetadata={() => console.log("Video metadata loaded")}
                        onError={(e) => console.error("Video element error:", e)}
                    />

                    {/* Loading Overlay */}
                    {isConnecting && !connectionError && (
                        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-70 text-white">
                            <div className="text-center">
                                <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-white mx-auto mb-3"></div>
                                <p>Connecting to device camera...</p>
                                <p className="text-sm text-gray-400 mt-1">Attempt {connectionAttemptsRef.current} of {MAX_CONNECTION_ATTEMPTS}</p>
                            </div>
                        </div>
                    )}

                    {/* Error Overlay */}
                    {connectionError && (
                        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-80 text-white p-4">
                            <div className="text-center">
                                <AlertTriangle size={40} className="text-red-500 mx-auto mb-3" />
                                <h3 className="text-xl font-semibold mb-2">Connection Error</h3>
                                <p className="mb-4 text-gray-300">{connectionError}</p>
                                {connectionAttemptsRef.current < MAX_CONNECTION_ATTEMPTS && (
                                    <button
                                        className="mt-2 px-5 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                                        onClick={handleRetry}
                                    >
                                        Try Again
                                    </button>
                                )}
                                <button
                                        className="mt-2 ml-2 px-5 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 focus:outline-none"
                                        onClick={onClose} // Allow closing even on error
                                    >
                                        Close
                                    </button>
                            </div>
                        </div>
                    )}

                    {/* Live indicator and controls (Show only when connected) */}
                    {!isConnecting && !connectionError && (
                        <>
                            {/* Live indicator */}
                            <div className="absolute top-4 left-4 flex items-center bg-red-600 text-white px-2 py-1 rounded-sm text-xs shadow">
                                <span className="w-2 h-2 bg-white rounded-full mr-1.5 animate-pulse"></span>
                                LIVE
                            </div>

                            {/* Controls */}
                            <div className="absolute bottom-4 right-4 flex items-center space-x-2">
                                <button
                                    title={isMuted ? "Unmute Audio" : "Mute Audio"}
                                    onClick={toggleMute}
                                    className="bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-opacity"
                                >
                                    {isMuted ? <VolumeX size={20} /> : <Volume2 size={20} />}
                                </button>
                                <button
                                    title="Take Snapshot"
                                    onClick={handleSnapshot}
                                    className="bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-opacity"
                                >
                                    <Camera size={20} />
                                </button>
                            </div>
                        </>
                    )}
                </div>

                {/* Footer (Optional - can be removed if header is sufficient) */}
                 <div className="px-4 py-3 bg-gray-100 border-t border-gray-300 flex-shrink-0">
                    <div className="flex items-center justify-between">
                        <p className="text-xs text-gray-500">
                           {remainingTime ? `Session ends automatically.` : `Establishing connection...`}
                        </p>
                        <button
                            className="px-4 py-1.5 bg-gray-500 text-white text-sm rounded hover:bg-gray-600 focus:outline-none"
                            onClick={onClose}
                        >
                            Close View
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default LiveViewModal;