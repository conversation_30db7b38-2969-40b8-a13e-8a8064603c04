(function(Te,ye){typeof exports=="object"&&typeof module<"u"?module.exports=ye():typeof define=="function"&&define.amd?define(ye):(Te=typeof globalThis<"u"?globalThis:Te||self,Te.vmRTC=ye())})(this,function(){"use strict";function Te(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}function ye(n){if(n.__esModule)return n;var t=n.default;if(typeof t=="function"){var e=function r(){return this instanceof r?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};e.prototype=t.prototype}else e={};return Object.defineProperty(e,"__esModule",{value:!0}),Object.keys(n).forEach(function(r){var i=Object.getOwnPropertyDescriptor(n,r);Object.defineProperty(e,r,i.get?i:{enumerable:!0,get:function(){return n[r]}})}),e}var pt={exports:{}};(function(n){var t=Object.prototype.hasOwnProperty,e="~";function r(){}Object.create&&(r.prototype=Object.create(null),new r().__proto__||(e=!1));function i(u,l,d){this.fn=u,this.context=l,this.once=d||!1}function a(u,l,d,h,v){if(typeof d!="function")throw new TypeError("The listener must be a function");var y=new i(d,h||u,v),b=e?e+l:l;return u._events[b]?u._events[b].fn?u._events[b]=[u._events[b],y]:u._events[b].push(y):(u._events[b]=y,u._eventsCount++),u}function o(u,l){--u._eventsCount===0?u._events=new r:delete u._events[l]}function s(){this._events=new r,this._eventsCount=0}s.prototype.eventNames=function(){var l=[],d,h;if(this._eventsCount===0)return l;for(h in d=this._events)t.call(d,h)&&l.push(e?h.slice(1):h);return Object.getOwnPropertySymbols?l.concat(Object.getOwnPropertySymbols(d)):l},s.prototype.listeners=function(l){var d=e?e+l:l,h=this._events[d];if(!h)return[];if(h.fn)return[h.fn];for(var v=0,y=h.length,b=new Array(y);v<y;v++)b[v]=h[v].fn;return b},s.prototype.listenerCount=function(l){var d=e?e+l:l,h=this._events[d];return h?h.fn?1:h.length:0},s.prototype.emit=function(l,d,h,v,y,b){var T=e?e+l:l;if(!this._events[T])return!1;var S=this._events[T],R=arguments.length,U,M;if(S.fn){switch(S.once&&this.removeListener(l,S.fn,void 0,!0),R){case 1:return S.fn.call(S.context),!0;case 2:return S.fn.call(S.context,d),!0;case 3:return S.fn.call(S.context,d,h),!0;case 4:return S.fn.call(S.context,d,h,v),!0;case 5:return S.fn.call(S.context,d,h,v,y),!0;case 6:return S.fn.call(S.context,d,h,v,y,b),!0}for(M=1,U=new Array(R-1);M<R;M++)U[M-1]=arguments[M];S.fn.apply(S.context,U)}else{var K=S.length,B;for(M=0;M<K;M++)switch(S[M].once&&this.removeListener(l,S[M].fn,void 0,!0),R){case 1:S[M].fn.call(S[M].context);break;case 2:S[M].fn.call(S[M].context,d);break;case 3:S[M].fn.call(S[M].context,d,h);break;case 4:S[M].fn.call(S[M].context,d,h,v);break;default:if(!U)for(B=1,U=new Array(R-1);B<R;B++)U[B-1]=arguments[B];S[M].fn.apply(S[M].context,U)}}return!0},s.prototype.on=function(l,d,h){return a(this,l,d,h,!1)},s.prototype.once=function(l,d,h){return a(this,l,d,h,!0)},s.prototype.removeListener=function(l,d,h,v){var y=e?e+l:l;if(!this._events[y])return this;if(!d)return o(this,y),this;var b=this._events[y];if(b.fn)b.fn===d&&(!v||b.once)&&(!h||b.context===h)&&o(this,y);else{for(var T=0,S=[],R=b.length;T<R;T++)(b[T].fn!==d||v&&!b[T].once||h&&b[T].context!==h)&&S.push(b[T]);S.length?this._events[y]=S.length===1?S[0]:S:o(this,y)}return this},s.prototype.removeAllListeners=function(l){var d;return l?(d=e?e+l:l,this._events[d]&&o(this,d)):(this._events=new r,this._eventsCount=0),this},s.prototype.off=s.prototype.removeListener,s.prototype.addListener=s.prototype.on,s.prefixed=e,s.EventEmitter=s,n.exports=s})(pt);var on=pt.exports;const sn=Te(on),W=new sn,cn=1,ln=0,un=2,dn={host:"staging-api.visionmaxfleet.com",apiKey:null},fn={host:"dev-api.visionmaxfleet.com",apiKey:null},gn={host:"api.visionmaxfleet.com",apiKey:null},k={info:"info",debug:"debug",error:"error",notice:"notice"},C=(n,t=null)=>{let r={info:"GreenYellow",debug:"yellow",error:"red",notice:"pink"}[t]??t;var i=new Error;if(!i.stack)try{throw i}catch(l){l.stack}var a=i.stack.toString().split(/\r\n|\n/);n===""&&(n='""');let o="@";a[0]=="Error"&&(a.shift(),o="(");let s=a[1].split(o);if(s.length<2&&(s[0]=s[0].replace("at","").trim()),s.length>1&&s[0],s.length>1?s[1].split(":"):s[0].split(":"),new URL(s[1]??s[0]).pathname.split("/").reverse()[0],r){console.log(`%c${n}`,`color:${r}`);return}else{console.log(n);return}},ge={region:"us-east-1",credential:{accessKeyId:null,secretAccessKey:null,sessionToken:null}},V={iotDeviceGateway:null,deviceCId:null,requestId:null,vid:null,region:"us-east-1"},$={credential:null,prefix:null,region:null,root:null,clientId:[],topics:[]},A={signalingName:null,signalingState:0,setting:null,role:"VIEWER",connectMode:"dataOnly",remoteView:null,localView:null,autoStreaming:!0,enableDataChannel:!0,CamEnableFlags:null,switchCameraButton:!1,capability:{video:!1,audio:!1},maxFailCounter:15,vmRTC_ReleasedVersion:null,is_vmRTC_VersionDeprecated:!1,is_vmRTC_VersionUnsupported:!1},hn=function(n,t){let r={dev:fn,staging:dn,prod:gn}[n]??!1;return r&&(r.apiKey=t),r},pn=function(n,t){const e=n,r=new window.AWS.Credentials(ge.credential.accessKeyId,ge.credential.secretAccessKey,ge.credential.sessionToken),i=window.AWS.util.date.iso8601(new Date).replace(/[:\\-]|\.\d{3}/g,""),a=i.substr(0,8),o="GET",s="wss",u="/mqtt",l="iotdevicegateway",d="AWS4-HMAC-SHA256",h=`${a}/${t}/${l}/aws4_request`;let v=`X-Amz-Algorithm=${d}`;v+=`&X-Amz-Credential=${encodeURIComponent(`${r.accessKeyId}/${h}`)}`,v+=`&X-Amz-Date=${i}`,v+="&X-Amz-SignedHeaders=host";const y=`host:${e}
`,b=window.AWS.util.crypto.sha256("","hex"),T=`${o}
${u}
${v}
${y}
host
${b}`,S=`${d}
${i}
${h}
${window.AWS.util.crypto.sha256(T,"hex")}`,R=mn(r.secretAccessKey,a,t,l),U=window.AWS.util.crypto.hmac(R,S,"hex");return v+=`&X-Amz-Signature=${U}`,v+=`&X-Amz-Security-Token=${encodeURIComponent(r.sessionToken)}`,`${s}://${e}${u}?${v}`},mn=function(t,e,r,i){const a=window.AWS.util.crypto.hmac(`AWS4${t}`,e,"buffer"),o=window.AWS.util.crypto.hmac(a,r,"buffer"),s=window.AWS.util.crypto.hmac(o,i,"buffer");return window.AWS.util.crypto.hmac(s,"aws4_request","buffer")},le={connecting:!0,connected:!1,connectMode:"dataOnly",connectInfoType:"inplace",playerType:"inplace",deviceCId:null,requestId:null,playerId:null,topics:[],options:null,actions:[],mqttClient:null,benchmark:[],CamEnableFlags:{},liveviewConfiguration:{},role:"VIEWER",callBacks:[],exposed:{requestId:null,deviceCId:null,peer:{viewer:null,master:null},peerReport(n=!1){var t,e,r,i,a,o,s,u,l,d,h,v;if(this.peer){console.log(`signalingState: ${(t=this.peer.viewer)==null?void 0:t.signalingState}`),console.log(`iceConnectionState: ${(e=this.peer.viewer)==null?void 0:e.iceConnectionState}`),console.log(`iceGatheringState: ${(r=this.peer.viewer)==null?void 0:r.iceGatheringState}`),console.log(`peer connectionState: ${(i=this.peer.viewer)==null?void 0:i.connectionState}`);let y=null,b=null;return(a=this.peer.viewer)==null||a.getReceivers().forEach(T=>{var S,R,U,M,K;y=`[Receiver] track:${(S=T.track)==null?void 0:S.kind}, ready:${(R=T.track)==null?void 0:R.readyState}, muted:${(U=T.track)==null?void 0:U.muted}, enabled:${(M=T.track)==null?void 0:M.enabled}, transport:${(K=T.transport)==null?void 0:K.state}`,console.log(y)}),(o=this.peer.viewer)==null||o.getSenders().forEach(T=>{var S,R,U,M,K;b=`[Sender] track:${(S=T.track)==null?void 0:S.kind}, label:${(R=T.track)==null?void 0:R.label}, muted:${(U=T.track)==null?void 0:U.muted}, enabled:${(M=T.track)==null?void 0:M.enabled}, transport:${(K=T.transport)==null?void 0:K.state}`,console.log(b)}),console.log("currentLocalDescription",(s=this.peer.viewer)==null?void 0:s.currentLocalDescription),console.log("currentRemoteDescription",(u=this.peer.viewer)==null?void 0:u.remoteDescription),{signalingState:(l=this.peer.viewer)==null?void 0:l.signalingState,iceConnectionState:(d=this.peer.viewer)==null?void 0:d.iceConnectionState,iceGatheringState:(h=this.peer.viewer)==null?void 0:h.iceGatheringState,connectionState:(v=this.peer.viewer)==null?void 0:v.connectionState,receivers:y,senders:b,requestId:this.requestId,deviceCid:this.deviceCId}}}}};let Ue;const vn=new Uint8Array(16);function yn(){if(!Ue&&(Ue=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!Ue))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return Ue(vn)}const wn=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;function bn(n){return typeof n=="string"&&wn.test(n)}const H=[];for(let n=0;n<256;++n)H.push((n+256).toString(16).slice(1));function mt(n,t=0){return H[n[t+0]]+H[n[t+1]]+H[n[t+2]]+H[n[t+3]]+"-"+H[n[t+4]]+H[n[t+5]]+"-"+H[n[t+6]]+H[n[t+7]]+"-"+H[n[t+8]]+H[n[t+9]]+"-"+H[n[t+10]]+H[n[t+11]]+H[n[t+12]]+H[n[t+13]]+H[n[t+14]]+H[n[t+15]]}function Sn(n){if(!bn(n))throw TypeError("Invalid UUID");let t;const e=new Uint8Array(16);return e[0]=(t=parseInt(n.slice(0,8),16))>>>24,e[1]=t>>>16&255,e[2]=t>>>8&255,e[3]=t&255,e[4]=(t=parseInt(n.slice(9,13),16))>>>8,e[5]=t&255,e[6]=(t=parseInt(n.slice(14,18),16))>>>8,e[7]=t&255,e[8]=(t=parseInt(n.slice(19,23),16))>>>8,e[9]=t&255,e[10]=(t=parseInt(n.slice(24,36),16))/1099511627776&255,e[11]=t/4294967296&255,e[12]=t>>>24&255,e[13]=t>>>16&255,e[14]=t>>>8&255,e[15]=t&255,e}function Cn(n){n=unescape(encodeURIComponent(n));const t=[];for(let e=0;e<n.length;++e)t.push(n.charCodeAt(e));return t}const _n="6ba7b810-9dad-11d1-80b4-00c04fd430c8",An="6ba7b811-9dad-11d1-80b4-00c04fd430c8";function En(n,t,e){function r(i,a,o,s){var u;if(typeof i=="string"&&(i=Cn(i)),typeof a=="string"&&(a=Sn(a)),((u=a)===null||u===void 0?void 0:u.length)!==16)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");let l=new Uint8Array(16+i.length);if(l.set(a),l.set(i,a.length),l=e(l),l[6]=l[6]&15|t,l[8]=l[8]&63|128,o){s=s||0;for(let d=0;d<16;++d)o[s+d]=l[d];return o}return mt(l)}try{r.name=n}catch{}return r.DNS=_n,r.URL=An,r}const vt={randomUUID:typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};function Tn(n,t,e){if(vt.randomUUID&&!t&&!n)return vt.randomUUID();n=n||{};const r=n.random||(n.rng||yn)();if(r[6]=r[6]&15|64,r[8]=r[8]&63|128,t){e=e||0;for(let i=0;i<16;++i)t[e+i]=r[i];return t}return mt(r)}function On(n,t,e,r){switch(n){case 0:return t&e^~t&r;case 1:return t^e^r;case 2:return t&e^t&r^e&r;case 3:return t^e^r}}function Ge(n,t){return n<<t|n>>>32-t}function Mn(n){const t=[1518500249,1859775393,2400959708,3395469782],e=[1732584193,4023233417,2562383102,271733878,3285377520];if(typeof n=="string"){const o=unescape(encodeURIComponent(n));n=[];for(let s=0;s<o.length;++s)n.push(o.charCodeAt(s))}else Array.isArray(n)||(n=Array.prototype.slice.call(n));n.push(128);const r=n.length/4+2,i=Math.ceil(r/16),a=new Array(i);for(let o=0;o<i;++o){const s=new Uint32Array(16);for(let u=0;u<16;++u)s[u]=n[o*64+u*4]<<24|n[o*64+u*4+1]<<16|n[o*64+u*4+2]<<8|n[o*64+u*4+3];a[o]=s}a[i-1][14]=(n.length-1)*8/Math.pow(2,32),a[i-1][14]=Math.floor(a[i-1][14]),a[i-1][15]=(n.length-1)*8&4294967295;for(let o=0;o<i;++o){const s=new Uint32Array(80);for(let y=0;y<16;++y)s[y]=a[o][y];for(let y=16;y<80;++y)s[y]=Ge(s[y-3]^s[y-8]^s[y-14]^s[y-16],1);let u=e[0],l=e[1],d=e[2],h=e[3],v=e[4];for(let y=0;y<80;++y){const b=Math.floor(y/20),T=Ge(u,5)+On(b,l,d,h)+v+t[b]+s[y]>>>0;v=h,h=d,d=Ge(l,30)>>>0,l=u,u=T}e[0]=e[0]+u>>>0,e[1]=e[1]+l>>>0,e[2]=e[2]+d>>>0,e[3]=e[3]+h>>>0,e[4]=e[4]+v>>>0}return[e[0]>>24&255,e[0]>>16&255,e[0]>>8&255,e[0]&255,e[1]>>24&255,e[1]>>16&255,e[1]>>8&255,e[1]&255,e[2]>>24&255,e[2]>>16&255,e[2]>>8&255,e[2]&255,e[3]>>24&255,e[3]>>16&255,e[3]>>8&255,e[3]&255,e[4]>>24&255,e[4]>>16&255,e[4]>>8&255,e[4]&255]}const Dn=En("v5",80,Mn),In={initLiveView:"V1/devices/initialLiveView"},yt=(n="debug",t=null,e={})=>({level:n,msg:t,data:e});var Xe={},Je={};(function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.Role=void 0,function(t){t.MASTER="MASTER",t.VIEWER="VIEWER"}(n.Role||(n.Role={}))})(Je);var je={};/*! *****************************************************************************
	Copyright (c) Microsoft Corporation.

	Permission to use, copy, modify, and/or distribute this software for any
	purpose with or without fee is hereby granted.

	THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
	REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
	AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
	INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
	LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
	OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
	PERFORMANCE OF THIS SOFTWARE.
	***************************************************************************** */var Ze=function(n,t){return Ze=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,r){e.__proto__=r}||function(e,r){for(var i in r)r.hasOwnProperty(i)&&(e[i]=r[i])},Ze(n,t)};function kn(n,t){Ze(n,t);function e(){this.constructor=n}n.prototype=t===null?Object.create(t):(e.prototype=t.prototype,new e)}var et=function(){return et=Object.assign||function(t){for(var e,r=1,i=arguments.length;r<i;r++){e=arguments[r];for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a])}return t},et.apply(this,arguments)};function xn(n,t){var e={};for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&t.indexOf(r)<0&&(e[r]=n[r]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,r=Object.getOwnPropertySymbols(n);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(n,r[i])&&(e[r[i]]=n[r[i]]);return e}function Nn(n,t,e,r){var i=arguments.length,a=i<3?t:r===null?r=Object.getOwnPropertyDescriptor(t,e):r,o;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")a=Reflect.decorate(n,t,e,r);else for(var s=n.length-1;s>=0;s--)(o=n[s])&&(a=(i<3?o(a):i>3?o(t,e,a):o(t,e))||a);return i>3&&a&&Object.defineProperty(t,e,a),a}function Rn(n,t){return function(e,r){t(e,r,n)}}function Pn(n,t){if(typeof Reflect=="object"&&typeof Reflect.metadata=="function")return Reflect.metadata(n,t)}function Ln(n,t,e,r){function i(a){return a instanceof e?a:new e(function(o){o(a)})}return new(e||(e=Promise))(function(a,o){function s(d){try{l(r.next(d))}catch(h){o(h)}}function u(d){try{l(r.throw(d))}catch(h){o(h)}}function l(d){d.done?a(d.value):i(d.value).then(s,u)}l((r=r.apply(n,t||[])).next())})}function Un(n,t){var e={label:0,sent:function(){if(a[0]&1)throw a[1];return a[1]},trys:[],ops:[]},r,i,a,o;return o={next:s(0),throw:s(1),return:s(2)},typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o;function s(l){return function(d){return u([l,d])}}function u(l){if(r)throw new TypeError("Generator is already executing.");for(;e;)try{if(r=1,i&&(a=l[0]&2?i.return:l[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,l[1])).done)return a;switch(i=0,a&&(l=[l[0]&2,a.value]),l[0]){case 0:case 1:a=l;break;case 4:return e.label++,{value:l[1],done:!1};case 5:e.label++,i=l[1],l=[0];continue;case 7:l=e.ops.pop(),e.trys.pop();continue;default:if(a=e.trys,!(a=a.length>0&&a[a.length-1])&&(l[0]===6||l[0]===2)){e=0;continue}if(l[0]===3&&(!a||l[1]>a[0]&&l[1]<a[3])){e.label=l[1];break}if(l[0]===6&&e.label<a[1]){e.label=a[1],a=l;break}if(a&&e.label<a[2]){e.label=a[2],e.ops.push(l);break}a[2]&&e.ops.pop(),e.trys.pop();continue}l=t.call(n,e)}catch(d){l=[6,d],i=0}finally{r=a=0}if(l[0]&5)throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}}function jn(n,t,e,r){r===void 0&&(r=e),n[r]=t[e]}function $n(n,t){for(var e in n)e!=="default"&&!t.hasOwnProperty(e)&&(t[e]=n[e])}function tt(n){var t=typeof Symbol=="function"&&Symbol.iterator,e=t&&n[t],r=0;if(e)return e.call(n);if(n&&typeof n.length=="number")return{next:function(){return n&&r>=n.length&&(n=void 0),{value:n&&n[r++],done:!n}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function wt(n,t){var e=typeof Symbol=="function"&&n[Symbol.iterator];if(!e)return n;var r=e.call(n),i,a=[],o;try{for(;(t===void 0||t-- >0)&&!(i=r.next()).done;)a.push(i.value)}catch(s){o={error:s}}finally{try{i&&!i.done&&(e=r.return)&&e.call(r)}finally{if(o)throw o.error}}return a}function Wn(){for(var n=[],t=0;t<arguments.length;t++)n=n.concat(wt(arguments[t]));return n}function Vn(){for(var n=0,t=0,e=arguments.length;t<e;t++)n+=arguments[t].length;for(var r=Array(n),i=0,t=0;t<e;t++)for(var a=arguments[t],o=0,s=a.length;o<s;o++,i++)r[i]=a[o];return r}function Oe(n){return this instanceof Oe?(this.v=n,this):new Oe(n)}function qn(n,t,e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=e.apply(n,t||[]),i,a=[];return i={},o("next"),o("throw"),o("return"),i[Symbol.asyncIterator]=function(){return this},i;function o(v){r[v]&&(i[v]=function(y){return new Promise(function(b,T){a.push([v,y,b,T])>1||s(v,y)})})}function s(v,y){try{u(r[v](y))}catch(b){h(a[0][3],b)}}function u(v){v.value instanceof Oe?Promise.resolve(v.value.v).then(l,d):h(a[0][2],v)}function l(v){s("next",v)}function d(v){s("throw",v)}function h(v,y){v(y),a.shift(),a.length&&s(a[0][0],a[0][1])}}function Kn(n){var t,e;return t={},r("next"),r("throw",function(i){throw i}),r("return"),t[Symbol.iterator]=function(){return this},t;function r(i,a){t[i]=n[i]?function(o){return(e=!e)?{value:Oe(n[i](o)),done:i==="return"}:a?a(o):o}:a}}function Hn(n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=n[Symbol.asyncIterator],e;return t?t.call(n):(n=typeof tt=="function"?tt(n):n[Symbol.iterator](),e={},r("next"),r("throw"),r("return"),e[Symbol.asyncIterator]=function(){return this},e);function r(a){e[a]=n[a]&&function(o){return new Promise(function(s,u){o=n[a](o),i(s,u,o.done,o.value)})}}function i(a,o,s,u){Promise.resolve(u).then(function(l){a({value:l,done:s})},o)}}function Yn(n,t){return Object.defineProperty?Object.defineProperty(n,"raw",{value:t}):n.raw=t,n}function Fn(n){if(n&&n.__esModule)return n;var t={};if(n!=null)for(var e in n)Object.hasOwnProperty.call(n,e)&&(t[e]=n[e]);return t.default=n,t}function Bn(n){return n&&n.__esModule?n:{default:n}}function Qn(n,t){if(!t.has(n))throw new TypeError("attempted to get private field on non-instance");return t.get(n)}function zn(n,t,e){if(!t.has(n))throw new TypeError("attempted to set private field on non-instance");return t.set(n,e),e}const bt=ye(Object.freeze(Object.defineProperty({__proto__:null,get __assign(){return et},__asyncDelegator:Kn,__asyncGenerator:qn,__asyncValues:Hn,__await:Oe,__awaiter:Ln,__classPrivateFieldGet:Qn,__classPrivateFieldSet:zn,__createBinding:jn,__decorate:Nn,__exportStar:$n,__extends:kn,__generator:Un,__importDefault:Bn,__importStar:Fn,__makeTemplateObject:Yn,__metadata:Pn,__param:Rn,__read:wt,__rest:xn,__spread:Wn,__spreadArrays:Vn,__values:tt},Symbol.toStringTag,{value:"Module"})));var nt={exports:{}},we=typeof Reflect=="object"?Reflect:null,St=we&&typeof we.apply=="function"?we.apply:function(t,e,r){return Function.prototype.apply.call(t,e,r)},$e;we&&typeof we.ownKeys=="function"?$e=we.ownKeys:Object.getOwnPropertySymbols?$e=function(t){return Object.getOwnPropertyNames(t).concat(Object.getOwnPropertySymbols(t))}:$e=function(t){return Object.getOwnPropertyNames(t)};function Gn(n){console&&console.warn&&console.warn(n)}var Ct=Number.isNaN||function(t){return t!==t};function N(){N.init.call(this)}nt.exports=N,nt.exports.once=er,N.EventEmitter=N,N.prototype._events=void 0,N.prototype._eventsCount=0,N.prototype._maxListeners=void 0;var _t=10;function We(n){if(typeof n!="function")throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof n)}Object.defineProperty(N,"defaultMaxListeners",{enumerable:!0,get:function(){return _t},set:function(n){if(typeof n!="number"||n<0||Ct(n))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+n+".");_t=n}}),N.init=function(){(this._events===void 0||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},N.prototype.setMaxListeners=function(t){if(typeof t!="number"||t<0||Ct(t))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+t+".");return this._maxListeners=t,this};function At(n){return n._maxListeners===void 0?N.defaultMaxListeners:n._maxListeners}N.prototype.getMaxListeners=function(){return At(this)},N.prototype.emit=function(t){for(var e=[],r=1;r<arguments.length;r++)e.push(arguments[r]);var i=t==="error",a=this._events;if(a!==void 0)i=i&&a.error===void 0;else if(!i)return!1;if(i){var o;if(e.length>0&&(o=e[0]),o instanceof Error)throw o;var s=new Error("Unhandled error."+(o?" ("+o.message+")":""));throw s.context=o,s}var u=a[t];if(u===void 0)return!1;if(typeof u=="function")St(u,this,e);else for(var l=u.length,d=Dt(u,l),r=0;r<l;++r)St(d[r],this,e);return!0};function Et(n,t,e,r){var i,a,o;if(We(e),a=n._events,a===void 0?(a=n._events=Object.create(null),n._eventsCount=0):(a.newListener!==void 0&&(n.emit("newListener",t,e.listener?e.listener:e),a=n._events),o=a[t]),o===void 0)o=a[t]=e,++n._eventsCount;else if(typeof o=="function"?o=a[t]=r?[e,o]:[o,e]:r?o.unshift(e):o.push(e),i=At(n),i>0&&o.length>i&&!o.warned){o.warned=!0;var s=new Error("Possible EventEmitter memory leak detected. "+o.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");s.name="MaxListenersExceededWarning",s.emitter=n,s.type=t,s.count=o.length,Gn(s)}return n}N.prototype.addListener=function(t,e){return Et(this,t,e,!1)},N.prototype.on=N.prototype.addListener,N.prototype.prependListener=function(t,e){return Et(this,t,e,!0)};function Xn(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function Tt(n,t,e){var r={fired:!1,wrapFn:void 0,target:n,type:t,listener:e},i=Xn.bind(r);return i.listener=e,r.wrapFn=i,i}N.prototype.once=function(t,e){return We(e),this.on(t,Tt(this,t,e)),this},N.prototype.prependOnceListener=function(t,e){return We(e),this.prependListener(t,Tt(this,t,e)),this},N.prototype.removeListener=function(t,e){var r,i,a,o,s;if(We(e),i=this._events,i===void 0)return this;if(r=i[t],r===void 0)return this;if(r===e||r.listener===e)--this._eventsCount===0?this._events=Object.create(null):(delete i[t],i.removeListener&&this.emit("removeListener",t,r.listener||e));else if(typeof r!="function"){for(a=-1,o=r.length-1;o>=0;o--)if(r[o]===e||r[o].listener===e){s=r[o].listener,a=o;break}if(a<0)return this;a===0?r.shift():Jn(r,a),r.length===1&&(i[t]=r[0]),i.removeListener!==void 0&&this.emit("removeListener",t,s||e)}return this},N.prototype.off=N.prototype.removeListener,N.prototype.removeAllListeners=function(t){var e,r,i;if(r=this._events,r===void 0)return this;if(r.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=0):r[t]!==void 0&&(--this._eventsCount===0?this._events=Object.create(null):delete r[t]),this;if(arguments.length===0){var a=Object.keys(r),o;for(i=0;i<a.length;++i)o=a[i],o!=="removeListener"&&this.removeAllListeners(o);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(e=r[t],typeof e=="function")this.removeListener(t,e);else if(e!==void 0)for(i=e.length-1;i>=0;i--)this.removeListener(t,e[i]);return this};function Ot(n,t,e){var r=n._events;if(r===void 0)return[];var i=r[t];return i===void 0?[]:typeof i=="function"?e?[i.listener||i]:[i]:e?Zn(i):Dt(i,i.length)}N.prototype.listeners=function(t){return Ot(this,t,!0)},N.prototype.rawListeners=function(t){return Ot(this,t,!1)},N.listenerCount=function(n,t){return typeof n.listenerCount=="function"?n.listenerCount(t):Mt.call(n,t)},N.prototype.listenerCount=Mt;function Mt(n){var t=this._events;if(t!==void 0){var e=t[n];if(typeof e=="function")return 1;if(e!==void 0)return e.length}return 0}N.prototype.eventNames=function(){return this._eventsCount>0?$e(this._events):[]};function Dt(n,t){for(var e=new Array(t),r=0;r<t;++r)e[r]=n[r];return e}function Jn(n,t){for(;t+1<n.length;t++)n[t]=n[t+1];n.pop()}function Zn(n){for(var t=new Array(n.length),e=0;e<t.length;++e)t[e]=n[e].listener||n[e];return t}function er(n,t){return new Promise(function(e,r){function i(o){n.removeListener(t,a),r(o)}function a(){typeof n.removeListener=="function"&&n.removeListener("error",i),e([].slice.call(arguments))}It(n,t,a,{once:!0}),t!=="error"&&tr(n,i,{once:!0})})}function tr(n,t,e){typeof n.on=="function"&&It(n,"error",t,e)}function It(n,t,e,r){if(typeof n.on=="function")r.once?n.once(t,e):n.on(t,e);else if(typeof n.addEventListener=="function")n.addEventListener(t,function i(a){r.once&&n.removeEventListener(t,i),e(a)});else throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof n)}var nr=nt.exports,Me={};/**
 * @file Web Cryptography API shim
 * <AUTHOR> S Vybornov <<EMAIL>>
 * @license MIT
 */(function(n,t){typeof define=="function"&&define.amd?define([],function(){return t(n)}):typeof module=="object"&&module.exports?module.exports=t(n):t(n)})(typeof self<"u"?self:globalThis,function(n){if(typeof Promise!="function")throw"Promise support required";var t=n.crypto||n.msCrypto;if(!t)return;var e=t.subtle||t.webkitSubtle;if(!e)return;var r=n.Crypto||t.constructor||Object,i=n.SubtleCrypto||e.constructor||Object;n.CryptoKey||n.Key;var a=n.navigator.userAgent.indexOf("Edge/")>-1,o=!!n.msCrypto&&!a,s=!t.subtle&&!!t.webkitSubtle;if(!o&&!s)return;function u(c){return btoa(c).replace(/\=+$/,"").replace(/\+/g,"-").replace(/\//g,"_")}function l(c){return c+="===",c=c.slice(0,-c.length%4),atob(c.replace(/-/g,"+").replace(/_/g,"/"))}function d(c){for(var f=new Uint8Array(c.length),w=0;w<c.length;w++)f[w]=c.charCodeAt(w);return f}function h(c){return c instanceof ArrayBuffer&&(c=new Uint8Array(c)),String.fromCharCode.apply(String,c)}function v(c){var f={name:(c.name||c||"").toUpperCase().replace("V","v")};switch(f.name){case"SHA-1":case"SHA-256":case"SHA-384":case"SHA-512":break;case"AES-CBC":case"AES-GCM":case"AES-KW":c.length&&(f.length=c.length);break;case"HMAC":c.hash&&(f.hash=v(c.hash)),c.length&&(f.length=c.length);break;case"RSAES-PKCS1-v1_5":c.publicExponent&&(f.publicExponent=new Uint8Array(c.publicExponent)),c.modulusLength&&(f.modulusLength=c.modulusLength);break;case"RSASSA-PKCS1-v1_5":case"RSA-OAEP":c.hash&&(f.hash=v(c.hash)),c.publicExponent&&(f.publicExponent=new Uint8Array(c.publicExponent)),c.modulusLength&&(f.modulusLength=c.modulusLength);break;default:throw new SyntaxError("Bad algorithm name")}return f}function y(c){return{HMAC:{"SHA-1":"HS1","SHA-256":"HS256","SHA-384":"HS384","SHA-512":"HS512"},"RSASSA-PKCS1-v1_5":{"SHA-1":"RS1","SHA-256":"RS256","SHA-384":"RS384","SHA-512":"RS512"},"RSAES-PKCS1-v1_5":{"":"RSA1_5"},"RSA-OAEP":{"SHA-1":"RSA-OAEP","SHA-256":"RSA-OAEP-256"},"AES-KW":{128:"A128KW",192:"A192KW",256:"A256KW"},"AES-GCM":{128:"A128GCM",192:"A192GCM",256:"A256GCM"},"AES-CBC":{128:"A128CBC",192:"A192CBC",256:"A256CBC"}}[c.name][(c.hash||{}).name||c.length||""]}function b(c){(c instanceof ArrayBuffer||c instanceof Uint8Array)&&(c=JSON.parse(decodeURIComponent(escape(h(c)))));var f={kty:c.kty,alg:c.alg,ext:c.ext||c.extractable};switch(f.kty){case"oct":f.k=c.k;case"RSA":["n","e","d","p","q","dp","dq","qi","oth"].forEach(function(w){w in c&&(f[w]=c[w])});break;default:throw new TypeError("Unsupported key type")}return f}function T(c){var f=b(c);return o&&(f.extractable=f.ext,delete f.ext),d(unescape(encodeURIComponent(JSON.stringify(f)))).buffer}function S(c){var f=K(c),w=!1;f.length>2&&(w=!0,f.shift());var p={ext:!0};switch(f[0][0]){case"1.2.840.113549.1.1.1":var D=["n","e","d","p","q","dp","dq","qi"],g=K(f[1]);w&&g.shift();for(var m=0;m<g.length;m++)g[m][0]||(g[m]=g[m].subarray(1)),p[D[m]]=u(h(g[m]));p.kty="RSA";break;default:throw new TypeError("Unsupported key type")}return p}function R(c){var f,w=[["",null]],p=!1;switch(c.kty){case"RSA":for(var D=["n","e","d","p","q","dp","dq","qi"],g=[],m=0;m<D.length&&D[m]in c;m++){var _=g[m]=d(l(c[D[m]]));_[0]&128&&(g[m]=new Uint8Array(_.length+1),g[m].set(_,1))}g.length>2&&(p=!0,g.unshift(new Uint8Array([0]))),w[0][0]="1.2.840.113549.1.1.1",f=g;break;default:throw new TypeError("Unsupported key type")}return w.push(new Uint8Array(B(f)).buffer),p?w.unshift(new Uint8Array([0])):w[1]={tag:3,value:w[1]},new Uint8Array(B(w)).buffer}var U={KoZIhvcNAQEB:"1.2.840.113549.1.1.1"},M={"1.2.840.113549.1.1.1":"KoZIhvcNAQEB"};function K(c,f){if(c instanceof ArrayBuffer&&(c=new Uint8Array(c)),f||(f={pos:0,end:c.length}),f.end-f.pos<2||f.end>c.length)throw new RangeError("Malformed DER");var w=c[f.pos++],p=c[f.pos++];if(p>=128){if(p&=127,f.end-f.pos<p)throw new RangeError("Malformed DER");for(var D=0;p--;)D<<=8,D|=c[f.pos++];p=D}if(f.end-f.pos<p)throw new RangeError("Malformed DER");var g;switch(w){case 2:g=c.subarray(f.pos,f.pos+=p);break;case 3:if(c[f.pos++])throw new Error("Unsupported bit string");p--;case 4:g=new Uint8Array(c.subarray(f.pos,f.pos+=p)).buffer;break;case 5:g=null;break;case 6:var m=btoa(h(c.subarray(f.pos,f.pos+=p)));if(!(m in U))throw new Error("Unsupported OBJECT ID "+m);g=U[m];break;case 48:g=[];for(var _=f.pos+p;f.pos<_;)g.push(K(c,f));break;default:throw new Error("Unsupported DER tag 0x"+w.toString(16))}return g}function B(c,f){f||(f=[]);var w=0,p=0,D=f.length+2;if(f.push(0,0),c instanceof Uint8Array){w=2,p=c.length;for(var g=0;g<p;g++)f.push(c[g])}else if(c instanceof ArrayBuffer){w=4,p=c.byteLength,c=new Uint8Array(c);for(var g=0;g<p;g++)f.push(c[g])}else if(c===null)w=5,p=0;else if(typeof c=="string"&&c in M){var m=d(atob(M[c]));w=6,p=m.length;for(var g=0;g<p;g++)f.push(m[g])}else if(c instanceof Array){for(var g=0;g<c.length;g++)B(c[g],f);w=48,p=f.length-D}else if(typeof c=="object"&&c.tag===3&&c.value instanceof ArrayBuffer){c=new Uint8Array(c.value),w=3,p=c.byteLength,f.push(0);for(var g=0;g<p;g++)f.push(c[g]);p++}else throw new Error("Unsupported DER value "+c);if(p>=128){var _=p,p=4;for(f.splice(D,0,_>>24&255,_>>16&255,_>>8&255,_&255);p>1&&!(_>>24);)_<<=8,p--;p<4&&f.splice(D,4-p),p|=128}return f.splice(D-2,2,w,p),f}function Q(c,f,w,p){Object.defineProperties(this,{_key:{value:c},type:{value:c.type,enumerable:!0},extractable:{value:w===void 0?c.extractable:w,enumerable:!0},algorithm:{value:f===void 0?c.algorithm:f,enumerable:!0},usages:{value:p===void 0?c.usages:p,enumerable:!0}})}function X(c){return c==="verify"||c==="encrypt"||c==="wrapKey"}function ae(c){return c==="sign"||c==="decrypt"||c==="unwrapKey"}if(["generateKey","importKey","unwrapKey"].forEach(function(c){var f=e[c];e[c]=function(w,p,D){var g=[].slice.call(arguments),m,_,F;switch(c){case"generateKey":m=v(w),_=p,F=D;break;case"importKey":m=v(D),_=g[3],F=g[4],w==="jwk"&&(p=b(p),p.alg||(p.alg=y(m)),p.key_ops||(p.key_ops=p.kty!=="oct"?"d"in p?F.filter(ae):F.filter(X):F.slice()),g[1]=T(p));break;case"unwrapKey":m=g[4],_=g[5],F=g[6],g[2]=D._key;break}if(c==="generateKey"&&m.name==="HMAC"&&m.hash)return m.length=m.length||{"SHA-1":512,"SHA-256":512,"SHA-384":1024,"SHA-512":1024}[m.hash.name],e.importKey("raw",t.getRandomValues(new Uint8Array(m.length+7>>3)),m,_,F);if(s&&c==="generateKey"&&m.name==="RSASSA-PKCS1-v1_5"&&(!m.modulusLength||m.modulusLength>=2048))return w=v(w),w.name="RSAES-PKCS1-v1_5",delete w.hash,e.generateKey(w,!0,["encrypt","decrypt"]).then(function(O){return Promise.all([e.exportKey("jwk",O.publicKey),e.exportKey("jwk",O.privateKey)])}).then(function(O){return O[0].alg=O[1].alg=y(m),O[0].key_ops=F.filter(X),O[1].key_ops=F.filter(ae),Promise.all([e.importKey("jwk",O[0],m,!0,O[0].key_ops),e.importKey("jwk",O[1],m,_,O[1].key_ops)])}).then(function(O){return{publicKey:O[0],privateKey:O[1]}});if((s||o&&(m.hash||{}).name==="SHA-1")&&c==="importKey"&&w==="jwk"&&m.name==="HMAC"&&p.kty==="oct")return e.importKey("raw",d(l(p.k)),D,g[3],g[4]);if(s&&c==="importKey"&&(w==="spki"||w==="pkcs8"))return e.importKey("jwk",S(p),D,g[3],g[4]);if(o&&c==="unwrapKey")return e.decrypt(g[3],D,p).then(function(O){return e.importKey(w,O,g[4],g[5],g[6])});var q;try{q=f.apply(e,g)}catch(O){return Promise.reject(O)}return o&&(q=new Promise(function(O,gt){q.onabort=q.onerror=function(Le){gt(Le)},q.oncomplete=function(Le){O(Le.target.result)}})),q=q.then(function(O){return m.name==="HMAC"&&(m.length||(m.length=8*O.algorithm.length)),m.name.search("RSA")==0&&(m.modulusLength||(m.modulusLength=(O.publicKey||O).algorithm.modulusLength),m.publicExponent||(m.publicExponent=(O.publicKey||O).algorithm.publicExponent)),O.publicKey&&O.privateKey?O={publicKey:new Q(O.publicKey,m,_,F.filter(X)),privateKey:new Q(O.privateKey,m,_,F.filter(ae))}:O=new Q(O,m,_,F),O}),q}}),["exportKey","wrapKey"].forEach(function(c){var f=e[c];e[c]=function(w,p,D){var g=[].slice.call(arguments);switch(c){case"exportKey":g[1]=p._key;break;case"wrapKey":g[1]=p._key,g[2]=D._key;break}if((s||o&&(p.algorithm.hash||{}).name==="SHA-1")&&c==="exportKey"&&w==="jwk"&&p.algorithm.name==="HMAC"&&(g[0]="raw"),s&&c==="exportKey"&&(w==="spki"||w==="pkcs8")&&(g[0]="jwk"),o&&c==="wrapKey")return e.exportKey(w,p).then(function(_){return w==="jwk"&&(_=d(unescape(encodeURIComponent(JSON.stringify(b(_)))))),e.encrypt(g[3],D,_)});var m;try{m=f.apply(e,g)}catch(_){return Promise.reject(_)}return o&&(m=new Promise(function(_,F){m.onabort=m.onerror=function(q){F(q)},m.oncomplete=function(q){_(q.target.result)}})),c==="exportKey"&&w==="jwk"&&(m=m.then(function(_){return(s||o&&(p.algorithm.hash||{}).name==="SHA-1")&&p.algorithm.name==="HMAC"?{kty:"oct",alg:y(p.algorithm),key_ops:p.usages.slice(),ext:!0,k:u(h(_))}:(_=b(_),_.alg||(_.alg=y(p.algorithm)),_.key_ops||(_.key_ops=p.type==="public"?p.usages.filter(X):p.type==="private"?p.usages.filter(ae):p.usages.slice()),_)})),s&&c==="exportKey"&&(w==="spki"||w==="pkcs8")&&(m=m.then(function(_){return _=R(b(_)),_})),m}}),["encrypt","decrypt","sign","verify"].forEach(function(c){var f=e[c];e[c]=function(w,p,D,g){if(o&&(!D.byteLength||g&&!g.byteLength))throw new Error("Empy input is not allowed");var m=[].slice.call(arguments),_=v(w);if(o&&c==="decrypt"&&_.name==="AES-GCM"){var F=w.tagLength>>3;m[2]=(D.buffer||D).slice(0,D.byteLength-F),w.tag=(D.buffer||D).slice(D.byteLength-F)}m[1]=p._key;var q;try{q=f.apply(e,m)}catch(O){return Promise.reject(O)}return o&&(q=new Promise(function(O,gt){q.onabort=q.onerror=function(Le){gt(Le)},q.oncomplete=function(oe){var oe=oe.target.result;if(c==="encrypt"&&oe instanceof AesGcmEncryptResult){var ht=oe.ciphertext,an=oe.tag;oe=new Uint8Array(ht.byteLength+an.byteLength),oe.set(new Uint8Array(ht),0),oe.set(new Uint8Array(an),ht.byteLength),oe=oe.buffer}O(oe)}})),q}}),o){var fe=e.digest;e.digest=function(c,f){if(!f.byteLength)throw new Error("Empy input is not allowed");var w;try{w=fe.call(e,c,f)}catch(p){return Promise.reject(p)}return w=new Promise(function(p,D){w.onabort=w.onerror=function(g){D(g)},w.oncomplete=function(g){p(g.target.result)}}),w},n.crypto=Object.create(t,{getRandomValues:{value:function(c){return t.getRandomValues(c)}},subtle:{value:e}}),n.CryptoKey=Q}s&&(t.subtle=e,n.Crypto=r,n.SubtleCrypto=i,n.CryptoKey=Q)});const rr=window.crypto,ir=ye(Object.freeze(Object.defineProperty({__proto__:null,default:rr},Symbol.toStringTag,{value:"Module"})));var he={},kt;function xt(){if(kt)return he;kt=1,Object.defineProperty(he,"__esModule",{value:!0}),he.validateValueNil=he.validateValueNonNil=void 0;function n(e,r){if(e===null)throw new Error(r+" cannot be null");if(e===void 0)throw new Error(r+" cannot be undefined");if(e==="")throw new Error(r+" cannot be empty")}he.validateValueNonNil=n;function t(e,r){if(e!=null&&e!=="")throw new Error(r+" should be null")}return he.validateValueNil=t,he}Object.defineProperty(Me,"__esModule",{value:!0}),Me.SigV4RequestSigner=void 0;var ce=bt,rt=ce.__importDefault(ir),Nt=xt(),ar=function(){function n(t,e,r){r===void 0&&(r=n.DEFAULT_SERVICE),this.region=t,this.credentials=e,this.service=r}return n.prototype.getSignedURL=function(t,e,r){return r===void 0&&(r=new Date),ce.__awaiter(this,void 0,void 0,function(){var i,a,o,s,u,l,d,h,v,y,b,T,S,R,U,M,K,B,Q,X,ae,fe,c;return ce.__generator(this,function(f){switch(f.label){case 0:return typeof this.credentials.getPromise!="function"?[3,2]:[4,this.credentials.getPromise()];case 1:f.sent(),f.label=2;case 2:if(Nt.validateValueNonNil(this.credentials.accessKeyId,"credentials.accessKeyId"),Nt.validateValueNonNil(this.credentials.secretAccessKey,"credentials.secretAccessKey"),i=n.getDateTimeString(r),a=n.getDateString(r),o="wss",s=o+"://",!t.startsWith(s))throw new Error("Endpoint '"+t+"' is not a secure WebSocket endpoint. It should start with '"+s+"'.");if(t.includes("?"))throw new Error("Endpoint '"+t+"' should not contain any query parameters.");return u=t.indexOf("/",s.length),u<0?(l=t.substring(s.length),d="/"):(l=t.substring(s.length,u),d=t.substring(u)),h=["host"].join(";"),v="GET",y=a+"/"+this.region+"/"+this.service+"/aws4_request",b=Object.assign({},e,{"X-Amz-Algorithm":n.DEFAULT_ALGORITHM,"X-Amz-Credential":this.credentials.accessKeyId+"/"+y,"X-Amz-Date":i,"X-Amz-Expires":"299","X-Amz-SignedHeaders":h}),this.credentials.sessionToken&&Object.assign(b,{"X-Amz-Security-Token":this.credentials.sessionToken}),T=n.createQueryString(b),S={host:l},R=n.createHeadersString(S),[4,n.sha256("")];case 3:return U=f.sent(),M=[v,d,T,R,h,U].join(`
`),[4,n.sha256(M)];case 4:return K=f.sent(),B=[n.DEFAULT_ALGORITHM,i,y,K].join(`
`),[4,this.getSignatureKey(a)];case 5:return Q=f.sent(),fe=(ae=n).toHex,[4,n.hmac(Q,B)];case 6:return[4,fe.apply(ae,[f.sent()])];case 7:return X=f.sent(),c=Object.assign({},b,{"X-Amz-Signature":X}),[2,o+"://"+l+d+"?"+n.createQueryString(c)]}})})},n.prototype.getSignatureKey=function(t){return ce.__awaiter(this,void 0,void 0,function(){var e,r,i;return ce.__generator(this,function(a){switch(a.label){case 0:return[4,n.hmac("AWS4"+this.credentials.secretAccessKey,t)];case 1:return e=a.sent(),[4,n.hmac(e,this.region)];case 2:return r=a.sent(),[4,n.hmac(r,this.service)];case 3:return i=a.sent(),[4,n.hmac(i,"aws4_request")];case 4:return[2,a.sent()]}})})},n.createHeadersString=function(t){return Object.keys(t).map(function(e){return e+":"+t[e]+`
`}).join()},n.createQueryString=function(t){return Object.keys(t).sort().map(function(e){return e+"="+encodeURIComponent(t[e])}).join("&")},n.getDateTimeString=function(t){return t.toISOString().replace(/\.\d{3}Z$/,"Z").replace(/[:\-]/g,"")},n.getDateString=function(t){return this.getDateTimeString(t).substring(0,8)},n.sha256=function(t){return ce.__awaiter(this,void 0,void 0,function(){var e;return ce.__generator(this,function(r){switch(r.label){case 0:return[4,rt.default.subtle.digest({name:"SHA-256"},this.toUint8Array(t))];case 1:return e=r.sent(),[2,this.toHex(e)]}})})},n.hmac=function(t,e){return ce.__awaiter(this,void 0,void 0,function(){var r,i,a;return ce.__generator(this,function(o){switch(o.label){case 0:return r=typeof t=="string"?this.toUint8Array(t).buffer:t,i=this.toUint8Array(e).buffer,[4,rt.default.subtle.importKey("raw",r,{name:"HMAC",hash:{name:"SHA-256"}},!1,["sign"])];case 1:return a=o.sent(),[4,rt.default.subtle.sign({name:"HMAC",hash:{name:"SHA-256"}},a,i)];case 2:return[2,o.sent()]}})})},n.toUint8Array=function(t){for(var e=new ArrayBuffer(t.length),r=new Uint8Array(e),i=0,a=t.length;i<a;i++)r[i]=t.charCodeAt(i);return r},n.toHex=function(t){return Array.from(new Uint8Array(t)).map(function(e){return e.toString(16).padStart(2,"0")}).join("")},n.DEFAULT_ALGORITHM="AWS4-HMAC-SHA256",n.DEFAULT_SERVICE="kinesisvideo",n}();Me.SigV4RequestSigner=ar;var Ve={},Rt;function or(){if(Rt)return Ve;Rt=1,Object.defineProperty(Ve,"__esModule",{value:!0});var n=function(){function t(e){this.clockOffsetMs=e}return t.prototype.getDate=function(){return new Date(Date.now()+this.clockOffsetMs)},t}();return Ve.default=n,Ve}Object.defineProperty(je,"__esModule",{value:!0}),je.SignalingClient=void 0;var De=bt,sr=nr,qe=Je,cr=Me,lr=De.__importDefault(or()),ue=xt(),ee;(function(n){n.SDP_ANSWER="SDP_ANSWER",n.SDP_OFFER="SDP_OFFER",n.ICE_CANDIDATE="ICE_CANDIDATE",n.BROADCAST="BROADCAST",n.RD_MANUALLY_TEST="RD_MANUALLY_TEST",n.VIEWER_ARRIVE="VIEWER_ARRIVE",n.VIEWER_LEAVE="VIEWER_LEAVE",n.MASTER_READY="MASTER_READY"})(ee||(ee={}));var te;(function(n){n[n.CONNECTING=0]="CONNECTING",n[n.OPEN=1]="OPEN",n[n.CLOSING=2]="CLOSING",n[n.CLOSED=3]="CLOSED"})(te||(te={}));var ur=function(n){De.__extends(t,n);function t(e){var r=n.call(this)||this;return r.websocket=null,r.readyState=te.CLOSED,r.pendingIceCandidatesByClientId={},r.hasReceivedRemoteSDPByClientId={},ue.validateValueNonNil(e,"SignalingClientConfig"),ue.validateValueNonNil(e.role,"role"),e.role===qe.Role.VIEWER?ue.validateValueNonNil(e.clientId,"clientId"):ue.validateValueNil(e.clientId,"clientId"),ue.validateValueNonNil(e.channelARN,"channelARN"),ue.validateValueNonNil(e.region,"region"),ue.validateValueNonNil(e.channelEndpoint,"channelEndpoint"),r.config=De.__assign({},e),e.requestSigner?r.requestSigner=e.requestSigner:(ue.validateValueNonNil(e.credentials,"credentials"),r.requestSigner=new cr.SigV4RequestSigner(e.region,e.credentials)),r.dateProvider=new lr.default(e.systemClockOffset||0),r.onOpen=r.onOpen.bind(r),r.onMessage=r.onMessage.bind(r),r.onError=r.onError.bind(r),r.onClose=r.onClose.bind(r),r}return t.prototype.open=function(e){var r=this;if(e===void 0&&(e=null),this.readyState!==te.CLOSED)throw new Error("Client is already open, opening, or closing");this.readyState=te.CONNECTING,this.asyncOpen(e).then().catch(function(i){return r.onError(i)})},t.prototype.asyncOpen=function(e){return e===void 0&&(e=null),De.__awaiter(this,void 0,void 0,function(){var r,i,a;return De.__generator(this,function(o){switch(o.label){case 0:return r=e,r?[3,2]:(i={"X-Amz-ChannelARN":this.config.channelARN},this.config.role===qe.Role.VIEWER&&(i["X-Amz-ClientId"]=this.config.clientId),[4,this.requestSigner.getSignedURL(this.config.channelEndpoint,i,this.dateProvider.getDate())]);case 1:a=o.sent(),r=a,o.label=2;case 2:return this.readyState!==te.CONNECTING?[2]:(console.log("opening signaling"),this.websocket=new WebSocket(r),this.websocket.addEventListener("open",this.onOpen),this.websocket.addEventListener("message",this.onMessage),this.websocket.addEventListener("error",this.onError),this.websocket.addEventListener("close",this.onClose),[2])}})})},t.prototype.close=function(){this.websocket!==null?(this.readyState=te.CLOSING,this.websocket.close()):this.readyState!==te.CLOSED&&this.onClose()},t.prototype.sendSdpOffer=function(e,r){this.sendMessage(ee.SDP_OFFER,e.toJSON(),r)},t.prototype.sendSdpAnswer=function(e,r){this.sendMessage(ee.SDP_ANSWER,e.toJSON(),r)},t.prototype.sendIceCandidate=function(e,r){this.sendMessage(ee.ICE_CANDIDATE,e.toJSON(),r)},t.prototype.sendNewViewerArrived=function(){},t.prototype.sendMasterReady=function(e){this.sendMessage(ee.MASTER_READY,null,e)},t.prototype.sendMessage=function(e,r,i){if(this.readyState!==te.OPEN)throw new Error("Could not send message because the connection to the signaling service is not open.");this.validateRecipientClientId(i),console.log(JSON.stringify({action:e,messagePayload:"omit",recipientClientId:i||void 0})),this.websocket.send(JSON.stringify({action:e,messagePayload:t.serializeJSONObjectAsBase64String(r),recipientClientId:i||void 0}))},t.prototype.cleanupWebSocket=function(){this.websocket!==null&&(this.websocket.removeEventListener("open",this.onOpen),this.websocket.removeEventListener("message",this.onMessage),this.websocket.removeEventListener("error",this.onError),this.websocket.removeEventListener("close",this.onClose),this.websocket=null)},t.prototype.onOpen=function(){this.readyState=te.OPEN,this.emit("open")},t.prototype.onMessage=function(e){var r,i;try{r=JSON.parse(e.data),i=t.parseJSONObjectFromBase64String(r.messagePayload)}catch{return}var a=r.messageType,o=r.senderClientId;switch(a){case ee.SDP_OFFER:this.emit("sdpOffer",i,o),this.emitPendingIceCandidates(o);return;case ee.SDP_ANSWER:this.emit("sdpAnswer",i,o),this.emitPendingIceCandidates(o);return;case ee.ICE_CANDIDATE:this.emitOrQueueIceCandidate(i,o);return;case ee.MASTER_READY:this.emit("masterReady",i,o);return;case ee.VIEWER_ARRIVE:this.emit("newViewer",i,o);return}},t.parseJSONObjectFromBase64String=function(e){return JSON.parse(atob(e))},t.serializeJSONObjectAsBase64String=function(e){return btoa(JSON.stringify(e))},t.prototype.emitOrQueueIceCandidate=function(e,r){var i=r||t.DEFAULT_CLIENT_ID;this.hasReceivedRemoteSDPByClientId[i]?this.emit("iceCandidate",e,r):(this.pendingIceCandidatesByClientId[i]||(this.pendingIceCandidatesByClientId[i]=[]),this.pendingIceCandidatesByClientId[i].push(e))},t.prototype.emitPendingIceCandidates=function(e){var r=this,i=e||t.DEFAULT_CLIENT_ID;this.hasReceivedRemoteSDPByClientId[i]=!0;var a=this.pendingIceCandidatesByClientId[i];a&&(delete this.pendingIceCandidatesByClientId[i],a.forEach(function(o){r.emit("iceCandidate",o,e)}))},t.prototype.validateRecipientClientId=function(e){if(this.config.role===qe.Role.MASTER&&!e)throw new Error("Missing recipient client id. As the MASTER, all messages must be sent with a recipient client id.");if(this.config.role===qe.Role.VIEWER&&e)throw new Error("Unexpected recipient client id. As the VIEWER, messages must not be sent with a recipient client id.")},t.prototype.onError=function(e){this.emit("error",e)},t.prototype.onClose=function(){this.readyState=te.CLOSED,this.cleanupWebSocket(),this.emit("close")},t.DEFAULT_CLIENT_ID="MASTER",t}(sr.EventEmitter);je.SignalingClient=ur,function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.VERSION=void 0;/*!
		Amazon Kinesis Video Streams WebRTC SDK for JavaScript
		Copyright 2019-2019 Amazon.com, Inc. or its affiliates. All Rights Reserved.

		This product includes software developed at
		Amazon Web Services, Inc. (http://aws.amazon.com/).
		*/var t=Je;Object.defineProperty(n,"Role",{enumerable:!0,get:function(){return t.Role}});var e=je;Object.defineProperty(n,"SignalingClient",{enumerable:!0,get:function(){return e.SignalingClient}});var r=Me;Object.defineProperty(n,"SigV4RequestSigner",{enumerable:!0,get:function(){return r.SigV4RequestSigner}})}(Xe);function dr(){return Math.random().toString(36).substring(2).toUpperCase()}const fr={region:"us-east-1",channelName:null,clientId:dr(),accessKeyId:null,secretAccessKey:null,sessionToken:null,endpoint:null,signaling_channel:null,ice_server_list:null,channel_arn:null,autoStreaming:!1,role:"VIEWER",liveSetting:{viewer:{sendVideo:!1,sendAudio:!1},master:{sendVideo:!0,sendAudio:!0},openDataChannel:!0,widescreen:!1,fullscreen:!1,useTrickleICE:!0,natTraversalDisabled:!1,forceTURN:!1},callBacks:{onResize:null,onCandidateTypeSelected:null,onConnectionStateChange(n){console.log(this,n)},onPeerCreateFail:null,onStateReport(n){n.forEach(t=>{window.adapter.browserDetails.browser==="firefox"||window.adapter.browserDetails.browser==="chrome"||window.adapter.browserDetails.browser})},onSignalingOpen:null,onSignalingClose:null,onSignalingError:null,onSdpOffer:null,onSdpAnswer:null,onIcecandidate:null,onRemoteDataMessage:null,onHostTypeSelected:null,onAllCandidatesGenerated:null,onCandidate:null,onnegotiationneeded:null}};function Ke(n){"@babel/helpers - typeof";return Ke=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ke(n)}function pe(n){if(n===null||n===!0||n===!1)return NaN;var t=Number(n);return isNaN(t)?t:t<0?Math.ceil(t):Math.floor(t)}function Y(n,t){if(t.length<n)throw new TypeError(n+" argument"+(n>1?"s":"")+" required, but only "+t.length+" present")}function J(n){Y(1,arguments);var t=Object.prototype.toString.call(n);return n instanceof Date||Ke(n)==="object"&&t==="[object Date]"?new Date(n.getTime()):typeof n=="number"||t==="[object Number]"?new Date(n):((typeof n=="string"||t==="[object String]")&&typeof console<"u"&&(console.warn("Starting with v2.0.0-beta.1 date-fns doesn't accept strings as date arguments. Please use `parseISO` to parse strings. See: https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#string-arguments"),console.warn(new Error().stack)),new Date(NaN))}function gr(n,t){Y(2,arguments);var e=J(n).getTime(),r=pe(t);return new Date(e+r)}var hr={};function He(){return hr}function pr(n){var t=new Date(Date.UTC(n.getFullYear(),n.getMonth(),n.getDate(),n.getHours(),n.getMinutes(),n.getSeconds(),n.getMilliseconds()));return t.setUTCFullYear(n.getFullYear()),n.getTime()-t.getTime()}function mr(n){Y(1,arguments);var t=J(n);return t.setHours(0,0,0,0),t}function vr(n){return Y(1,arguments),n instanceof Date||Ke(n)==="object"&&Object.prototype.toString.call(n)==="[object Date]"}function yr(n){if(Y(1,arguments),!vr(n)&&typeof n!="number")return!1;var t=J(n);return!isNaN(Number(t))}function wr(n){Y(1,arguments);var t=J(n);return t.setHours(23,59,59,999),t}function br(n,t){Y(2,arguments);var e=pe(t);return gr(n,-e)}var Sr=864e5;function Cr(n){Y(1,arguments);var t=J(n),e=t.getTime();t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0);var r=t.getTime(),i=e-r;return Math.floor(i/Sr)+1}function Ye(n){Y(1,arguments);var t=1,e=J(n),r=e.getUTCDay(),i=(r<t?7:0)+r-t;return e.setUTCDate(e.getUTCDate()-i),e.setUTCHours(0,0,0,0),e}function Pt(n){Y(1,arguments);var t=J(n),e=t.getUTCFullYear(),r=new Date(0);r.setUTCFullYear(e+1,0,4),r.setUTCHours(0,0,0,0);var i=Ye(r),a=new Date(0);a.setUTCFullYear(e,0,4),a.setUTCHours(0,0,0,0);var o=Ye(a);return t.getTime()>=i.getTime()?e+1:t.getTime()>=o.getTime()?e:e-1}function _r(n){Y(1,arguments);var t=Pt(n),e=new Date(0);e.setUTCFullYear(t,0,4),e.setUTCHours(0,0,0,0);var r=Ye(e);return r}var Ar=6048e5;function Er(n){Y(1,arguments);var t=J(n),e=Ye(t).getTime()-_r(t).getTime();return Math.round(e/Ar)+1}function Fe(n,t){var e,r,i,a,o,s,u,l;Y(1,arguments);var d=He(),h=pe((e=(r=(i=(a=t==null?void 0:t.weekStartsOn)!==null&&a!==void 0?a:t==null||(o=t.locale)===null||o===void 0||(s=o.options)===null||s===void 0?void 0:s.weekStartsOn)!==null&&i!==void 0?i:d.weekStartsOn)!==null&&r!==void 0?r:(u=d.locale)===null||u===void 0||(l=u.options)===null||l===void 0?void 0:l.weekStartsOn)!==null&&e!==void 0?e:0);if(!(h>=0&&h<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");var v=J(n),y=v.getUTCDay(),b=(y<h?7:0)+y-h;return v.setUTCDate(v.getUTCDate()-b),v.setUTCHours(0,0,0,0),v}function Lt(n,t){var e,r,i,a,o,s,u,l;Y(1,arguments);var d=J(n),h=d.getUTCFullYear(),v=He(),y=pe((e=(r=(i=(a=t==null?void 0:t.firstWeekContainsDate)!==null&&a!==void 0?a:t==null||(o=t.locale)===null||o===void 0||(s=o.options)===null||s===void 0?void 0:s.firstWeekContainsDate)!==null&&i!==void 0?i:v.firstWeekContainsDate)!==null&&r!==void 0?r:(u=v.locale)===null||u===void 0||(l=u.options)===null||l===void 0?void 0:l.firstWeekContainsDate)!==null&&e!==void 0?e:1);if(!(y>=1&&y<=7))throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var b=new Date(0);b.setUTCFullYear(h+1,0,y),b.setUTCHours(0,0,0,0);var T=Fe(b,t),S=new Date(0);S.setUTCFullYear(h,0,y),S.setUTCHours(0,0,0,0);var R=Fe(S,t);return d.getTime()>=T.getTime()?h+1:d.getTime()>=R.getTime()?h:h-1}function Tr(n,t){var e,r,i,a,o,s,u,l;Y(1,arguments);var d=He(),h=pe((e=(r=(i=(a=t==null?void 0:t.firstWeekContainsDate)!==null&&a!==void 0?a:t==null||(o=t.locale)===null||o===void 0||(s=o.options)===null||s===void 0?void 0:s.firstWeekContainsDate)!==null&&i!==void 0?i:d.firstWeekContainsDate)!==null&&r!==void 0?r:(u=d.locale)===null||u===void 0||(l=u.options)===null||l===void 0?void 0:l.firstWeekContainsDate)!==null&&e!==void 0?e:1),v=Lt(n,t),y=new Date(0);y.setUTCFullYear(v,0,h),y.setUTCHours(0,0,0,0);var b=Fe(y,t);return b}var Or=6048e5;function Mr(n,t){Y(1,arguments);var e=J(n),r=Fe(e,t).getTime()-Tr(e,t).getTime();return Math.round(r/Or)+1}function x(n,t){for(var e=n<0?"-":"",r=Math.abs(n).toString();r.length<t;)r="0"+r;return e+r}var Dr={y:function(t,e){var r=t.getUTCFullYear(),i=r>0?r:1-r;return x(e==="yy"?i%100:i,e.length)},M:function(t,e){var r=t.getUTCMonth();return e==="M"?String(r+1):x(r+1,2)},d:function(t,e){return x(t.getUTCDate(),e.length)},a:function(t,e){var r=t.getUTCHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return r.toUpperCase();case"aaa":return r;case"aaaaa":return r[0];case"aaaa":default:return r==="am"?"a.m.":"p.m."}},h:function(t,e){return x(t.getUTCHours()%12||12,e.length)},H:function(t,e){return x(t.getUTCHours(),e.length)},m:function(t,e){return x(t.getUTCMinutes(),e.length)},s:function(t,e){return x(t.getUTCSeconds(),e.length)},S:function(t,e){var r=e.length,i=t.getUTCMilliseconds(),a=Math.floor(i*Math.pow(10,r-3));return x(a,e.length)}};const de=Dr;var be={am:"am",pm:"pm",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},Ir={G:function(t,e,r){var i=t.getUTCFullYear()>0?1:0;switch(e){case"G":case"GG":case"GGG":return r.era(i,{width:"abbreviated"});case"GGGGG":return r.era(i,{width:"narrow"});case"GGGG":default:return r.era(i,{width:"wide"})}},y:function(t,e,r){if(e==="yo"){var i=t.getUTCFullYear(),a=i>0?i:1-i;return r.ordinalNumber(a,{unit:"year"})}return de.y(t,e)},Y:function(t,e,r,i){var a=Lt(t,i),o=a>0?a:1-a;if(e==="YY"){var s=o%100;return x(s,2)}return e==="Yo"?r.ordinalNumber(o,{unit:"year"}):x(o,e.length)},R:function(t,e){var r=Pt(t);return x(r,e.length)},u:function(t,e){var r=t.getUTCFullYear();return x(r,e.length)},Q:function(t,e,r){var i=Math.ceil((t.getUTCMonth()+1)/3);switch(e){case"Q":return String(i);case"QQ":return x(i,2);case"Qo":return r.ordinalNumber(i,{unit:"quarter"});case"QQQ":return r.quarter(i,{width:"abbreviated",context:"formatting"});case"QQQQQ":return r.quarter(i,{width:"narrow",context:"formatting"});case"QQQQ":default:return r.quarter(i,{width:"wide",context:"formatting"})}},q:function(t,e,r){var i=Math.ceil((t.getUTCMonth()+1)/3);switch(e){case"q":return String(i);case"qq":return x(i,2);case"qo":return r.ordinalNumber(i,{unit:"quarter"});case"qqq":return r.quarter(i,{width:"abbreviated",context:"standalone"});case"qqqqq":return r.quarter(i,{width:"narrow",context:"standalone"});case"qqqq":default:return r.quarter(i,{width:"wide",context:"standalone"})}},M:function(t,e,r){var i=t.getUTCMonth();switch(e){case"M":case"MM":return de.M(t,e);case"Mo":return r.ordinalNumber(i+1,{unit:"month"});case"MMM":return r.month(i,{width:"abbreviated",context:"formatting"});case"MMMMM":return r.month(i,{width:"narrow",context:"formatting"});case"MMMM":default:return r.month(i,{width:"wide",context:"formatting"})}},L:function(t,e,r){var i=t.getUTCMonth();switch(e){case"L":return String(i+1);case"LL":return x(i+1,2);case"Lo":return r.ordinalNumber(i+1,{unit:"month"});case"LLL":return r.month(i,{width:"abbreviated",context:"standalone"});case"LLLLL":return r.month(i,{width:"narrow",context:"standalone"});case"LLLL":default:return r.month(i,{width:"wide",context:"standalone"})}},w:function(t,e,r,i){var a=Mr(t,i);return e==="wo"?r.ordinalNumber(a,{unit:"week"}):x(a,e.length)},I:function(t,e,r){var i=Er(t);return e==="Io"?r.ordinalNumber(i,{unit:"week"}):x(i,e.length)},d:function(t,e,r){return e==="do"?r.ordinalNumber(t.getUTCDate(),{unit:"date"}):de.d(t,e)},D:function(t,e,r){var i=Cr(t);return e==="Do"?r.ordinalNumber(i,{unit:"dayOfYear"}):x(i,e.length)},E:function(t,e,r){var i=t.getUTCDay();switch(e){case"E":case"EE":case"EEE":return r.day(i,{width:"abbreviated",context:"formatting"});case"EEEEE":return r.day(i,{width:"narrow",context:"formatting"});case"EEEEEE":return r.day(i,{width:"short",context:"formatting"});case"EEEE":default:return r.day(i,{width:"wide",context:"formatting"})}},e:function(t,e,r,i){var a=t.getUTCDay(),o=(a-i.weekStartsOn+8)%7||7;switch(e){case"e":return String(o);case"ee":return x(o,2);case"eo":return r.ordinalNumber(o,{unit:"day"});case"eee":return r.day(a,{width:"abbreviated",context:"formatting"});case"eeeee":return r.day(a,{width:"narrow",context:"formatting"});case"eeeeee":return r.day(a,{width:"short",context:"formatting"});case"eeee":default:return r.day(a,{width:"wide",context:"formatting"})}},c:function(t,e,r,i){var a=t.getUTCDay(),o=(a-i.weekStartsOn+8)%7||7;switch(e){case"c":return String(o);case"cc":return x(o,e.length);case"co":return r.ordinalNumber(o,{unit:"day"});case"ccc":return r.day(a,{width:"abbreviated",context:"standalone"});case"ccccc":return r.day(a,{width:"narrow",context:"standalone"});case"cccccc":return r.day(a,{width:"short",context:"standalone"});case"cccc":default:return r.day(a,{width:"wide",context:"standalone"})}},i:function(t,e,r){var i=t.getUTCDay(),a=i===0?7:i;switch(e){case"i":return String(a);case"ii":return x(a,e.length);case"io":return r.ordinalNumber(a,{unit:"day"});case"iii":return r.day(i,{width:"abbreviated",context:"formatting"});case"iiiii":return r.day(i,{width:"narrow",context:"formatting"});case"iiiiii":return r.day(i,{width:"short",context:"formatting"});case"iiii":default:return r.day(i,{width:"wide",context:"formatting"})}},a:function(t,e,r){var i=t.getUTCHours(),a=i/12>=1?"pm":"am";switch(e){case"a":case"aa":return r.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"aaa":return r.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return r.dayPeriod(a,{width:"narrow",context:"formatting"});case"aaaa":default:return r.dayPeriod(a,{width:"wide",context:"formatting"})}},b:function(t,e,r){var i=t.getUTCHours(),a;switch(i===12?a=be.noon:i===0?a=be.midnight:a=i/12>=1?"pm":"am",e){case"b":case"bb":return r.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"bbb":return r.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return r.dayPeriod(a,{width:"narrow",context:"formatting"});case"bbbb":default:return r.dayPeriod(a,{width:"wide",context:"formatting"})}},B:function(t,e,r){var i=t.getUTCHours(),a;switch(i>=17?a=be.evening:i>=12?a=be.afternoon:i>=4?a=be.morning:a=be.night,e){case"B":case"BB":case"BBB":return r.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"BBBBB":return r.dayPeriod(a,{width:"narrow",context:"formatting"});case"BBBB":default:return r.dayPeriod(a,{width:"wide",context:"formatting"})}},h:function(t,e,r){if(e==="ho"){var i=t.getUTCHours()%12;return i===0&&(i=12),r.ordinalNumber(i,{unit:"hour"})}return de.h(t,e)},H:function(t,e,r){return e==="Ho"?r.ordinalNumber(t.getUTCHours(),{unit:"hour"}):de.H(t,e)},K:function(t,e,r){var i=t.getUTCHours()%12;return e==="Ko"?r.ordinalNumber(i,{unit:"hour"}):x(i,e.length)},k:function(t,e,r){var i=t.getUTCHours();return i===0&&(i=24),e==="ko"?r.ordinalNumber(i,{unit:"hour"}):x(i,e.length)},m:function(t,e,r){return e==="mo"?r.ordinalNumber(t.getUTCMinutes(),{unit:"minute"}):de.m(t,e)},s:function(t,e,r){return e==="so"?r.ordinalNumber(t.getUTCSeconds(),{unit:"second"}):de.s(t,e)},S:function(t,e){return de.S(t,e)},X:function(t,e,r,i){var a=i._originalDate||t,o=a.getTimezoneOffset();if(o===0)return"Z";switch(e){case"X":return jt(o);case"XXXX":case"XX":return me(o);case"XXXXX":case"XXX":default:return me(o,":")}},x:function(t,e,r,i){var a=i._originalDate||t,o=a.getTimezoneOffset();switch(e){case"x":return jt(o);case"xxxx":case"xx":return me(o);case"xxxxx":case"xxx":default:return me(o,":")}},O:function(t,e,r,i){var a=i._originalDate||t,o=a.getTimezoneOffset();switch(e){case"O":case"OO":case"OOO":return"GMT"+Ut(o,":");case"OOOO":default:return"GMT"+me(o,":")}},z:function(t,e,r,i){var a=i._originalDate||t,o=a.getTimezoneOffset();switch(e){case"z":case"zz":case"zzz":return"GMT"+Ut(o,":");case"zzzz":default:return"GMT"+me(o,":")}},t:function(t,e,r,i){var a=i._originalDate||t,o=Math.floor(a.getTime()/1e3);return x(o,e.length)},T:function(t,e,r,i){var a=i._originalDate||t,o=a.getTime();return x(o,e.length)}};function Ut(n,t){var e=n>0?"-":"+",r=Math.abs(n),i=Math.floor(r/60),a=r%60;if(a===0)return e+String(i);var o=t||"";return e+String(i)+o+x(a,2)}function jt(n,t){if(n%60===0){var e=n>0?"-":"+";return e+x(Math.abs(n)/60,2)}return me(n,t)}function me(n,t){var e=t||"",r=n>0?"-":"+",i=Math.abs(n),a=x(Math.floor(i/60),2),o=x(i%60,2);return r+a+e+o}const kr=Ir;var $t=function(t,e){switch(t){case"P":return e.date({width:"short"});case"PP":return e.date({width:"medium"});case"PPP":return e.date({width:"long"});case"PPPP":default:return e.date({width:"full"})}},Wt=function(t,e){switch(t){case"p":return e.time({width:"short"});case"pp":return e.time({width:"medium"});case"ppp":return e.time({width:"long"});case"pppp":default:return e.time({width:"full"})}},xr=function(t,e){var r=t.match(/(P+)(p+)?/)||[],i=r[1],a=r[2];if(!a)return $t(t,e);var o;switch(i){case"P":o=e.dateTime({width:"short"});break;case"PP":o=e.dateTime({width:"medium"});break;case"PPP":o=e.dateTime({width:"long"});break;case"PPPP":default:o=e.dateTime({width:"full"});break}return o.replace("{{date}}",$t(i,e)).replace("{{time}}",Wt(a,e))},Nr={p:Wt,P:xr};const Rr=Nr;var Pr=["D","DD"],Lr=["YY","YYYY"];function Ur(n){return Pr.indexOf(n)!==-1}function jr(n){return Lr.indexOf(n)!==-1}function Vt(n,t,e){if(n==="YYYY")throw new RangeError("Use `yyyy` instead of `YYYY` (in `".concat(t,"`) for formatting years to the input `").concat(e,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(n==="YY")throw new RangeError("Use `yy` instead of `YY` (in `".concat(t,"`) for formatting years to the input `").concat(e,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(n==="D")throw new RangeError("Use `d` instead of `D` (in `".concat(t,"`) for formatting days of the month to the input `").concat(e,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(n==="DD")throw new RangeError("Use `dd` instead of `DD` (in `".concat(t,"`) for formatting days of the month to the input `").concat(e,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"))}var $r={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},Wr=function(t,e,r){var i,a=$r[t];return typeof a=="string"?i=a:e===1?i=a.one:i=a.other.replace("{{count}}",e.toString()),r!=null&&r.addSuffix?r.comparison&&r.comparison>0?"in "+i:i+" ago":i};const Vr=Wr;function it(n){return function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},e=t.width?String(t.width):n.defaultWidth,r=n.formats[e]||n.formats[n.defaultWidth];return r}}var qr={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},Kr={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},Hr={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},Yr={date:it({formats:qr,defaultWidth:"full"}),time:it({formats:Kr,defaultWidth:"full"}),dateTime:it({formats:Hr,defaultWidth:"full"})};const Fr=Yr;var Br={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},Qr=function(t,e,r,i){return Br[t]};const zr=Qr;function Ie(n){return function(t,e){var r=e!=null&&e.context?String(e.context):"standalone",i;if(r==="formatting"&&n.formattingValues){var a=n.defaultFormattingWidth||n.defaultWidth,o=e!=null&&e.width?String(e.width):a;i=n.formattingValues[o]||n.formattingValues[a]}else{var s=n.defaultWidth,u=e!=null&&e.width?String(e.width):n.defaultWidth;i=n.values[u]||n.values[s]}var l=n.argumentCallback?n.argumentCallback(t):t;return i[l]}}var Gr={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},Xr={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},Jr={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},Zr={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},ei={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},ti={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},ni=function(t,e){var r=Number(t),i=r%100;if(i>20||i<10)switch(i%10){case 1:return r+"st";case 2:return r+"nd";case 3:return r+"rd"}return r+"th"},ri={ordinalNumber:ni,era:Ie({values:Gr,defaultWidth:"wide"}),quarter:Ie({values:Xr,defaultWidth:"wide",argumentCallback:function(t){return t-1}}),month:Ie({values:Jr,defaultWidth:"wide"}),day:Ie({values:Zr,defaultWidth:"wide"}),dayPeriod:Ie({values:ei,defaultWidth:"wide",formattingValues:ti,defaultFormattingWidth:"wide"})};const ii=ri;function ke(n){return function(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=e.width,i=r&&n.matchPatterns[r]||n.matchPatterns[n.defaultMatchWidth],a=t.match(i);if(!a)return null;var o=a[0],s=r&&n.parsePatterns[r]||n.parsePatterns[n.defaultParseWidth],u=Array.isArray(s)?oi(s,function(h){return h.test(o)}):ai(s,function(h){return h.test(o)}),l;l=n.valueCallback?n.valueCallback(u):u,l=e.valueCallback?e.valueCallback(l):l;var d=t.slice(o.length);return{value:l,rest:d}}}function ai(n,t){for(var e in n)if(n.hasOwnProperty(e)&&t(n[e]))return e}function oi(n,t){for(var e=0;e<n.length;e++)if(t(n[e]))return e}function si(n){return function(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=t.match(n.matchPattern);if(!r)return null;var i=r[0],a=t.match(n.parsePattern);if(!a)return null;var o=n.valueCallback?n.valueCallback(a[0]):a[0];o=e.valueCallback?e.valueCallback(o):o;var s=t.slice(i.length);return{value:o,rest:s}}}var ci=/^(\d+)(th|st|nd|rd)?/i,li=/\d+/i,ui={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},di={any:[/^b/i,/^(a|c)/i]},fi={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},gi={any:[/1/i,/2/i,/3/i,/4/i]},hi={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},pi={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},mi={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},vi={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},yi={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},wi={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},bi={ordinalNumber:si({matchPattern:ci,parsePattern:li,valueCallback:function(t){return parseInt(t,10)}}),era:ke({matchPatterns:ui,defaultMatchWidth:"wide",parsePatterns:di,defaultParseWidth:"any"}),quarter:ke({matchPatterns:fi,defaultMatchWidth:"wide",parsePatterns:gi,defaultParseWidth:"any",valueCallback:function(t){return t+1}}),month:ke({matchPatterns:hi,defaultMatchWidth:"wide",parsePatterns:pi,defaultParseWidth:"any"}),day:ke({matchPatterns:mi,defaultMatchWidth:"wide",parsePatterns:vi,defaultParseWidth:"any"}),dayPeriod:ke({matchPatterns:yi,defaultMatchWidth:"any",parsePatterns:wi,defaultParseWidth:"any"})},Si={code:"en-US",formatDistance:Vr,formatLong:Fr,formatRelative:zr,localize:ii,match:bi,options:{weekStartsOn:0,firstWeekContainsDate:1}};const Ci=Si;var _i=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,Ai=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,Ei=/^'([^]*?)'?$/,Ti=/''/g,Oi=/[a-zA-Z]/;function qt(n,t,e){var r,i,a,o,s,u,l,d,h,v,y,b,T,S,R,U,M,K;Y(2,arguments);var B=String(t),Q=He(),X=(r=(i=e==null?void 0:e.locale)!==null&&i!==void 0?i:Q.locale)!==null&&r!==void 0?r:Ci,ae=pe((a=(o=(s=(u=e==null?void 0:e.firstWeekContainsDate)!==null&&u!==void 0?u:e==null||(l=e.locale)===null||l===void 0||(d=l.options)===null||d===void 0?void 0:d.firstWeekContainsDate)!==null&&s!==void 0?s:Q.firstWeekContainsDate)!==null&&o!==void 0?o:(h=Q.locale)===null||h===void 0||(v=h.options)===null||v===void 0?void 0:v.firstWeekContainsDate)!==null&&a!==void 0?a:1);if(!(ae>=1&&ae<=7))throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var fe=pe((y=(b=(T=(S=e==null?void 0:e.weekStartsOn)!==null&&S!==void 0?S:e==null||(R=e.locale)===null||R===void 0||(U=R.options)===null||U===void 0?void 0:U.weekStartsOn)!==null&&T!==void 0?T:Q.weekStartsOn)!==null&&b!==void 0?b:(M=Q.locale)===null||M===void 0||(K=M.options)===null||K===void 0?void 0:K.weekStartsOn)!==null&&y!==void 0?y:0);if(!(fe>=0&&fe<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");if(!X.localize)throw new RangeError("locale must contain localize property");if(!X.formatLong)throw new RangeError("locale must contain formatLong property");var c=J(n);if(!yr(c))throw new RangeError("Invalid time value");var f=pr(c),w=br(c,f),p={firstWeekContainsDate:ae,weekStartsOn:fe,locale:X,_originalDate:c},D=B.match(Ai).map(function(g){var m=g[0];if(m==="p"||m==="P"){var _=Rr[m];return _(g,X.formatLong)}return g}).join("").match(_i).map(function(g){if(g==="''")return"'";var m=g[0];if(m==="'")return Mi(g);var _=kr[m];if(_)return!(e!=null&&e.useAdditionalWeekYearTokens)&&jr(g)&&Vt(g,t,String(n)),!(e!=null&&e.useAdditionalDayOfYearTokens)&&Ur(g)&&Vt(g,t,String(n)),_(w,g,X.localize,p);if(m.match(Oi))throw new RangeError("Format string contains an unescaped latin alphabet character `"+m+"`");return g}).join("");return D}function Mi(n){var t=n.match(Ei);return t?t[1].replace(Ti,"'"):n}const xe={rtcCommandCallBacks:{},getCams(n,t=null){typeof t=="function"&&(this.rtcCommandCallBacks.getCams=t),Ni()},ping(n,t){typeof t=="function"&&(this.rtcCommandCallBacks.ping=t),Ri(n)},switchCam(n,t){typeof t=="function"&&(this.rtcCommandCallBacks.switchCam=t),Yt(n)}};let at=null,Kt=null,ot=null;ot=null,qt(mr(new Date),"t"),qt(wr(new Date),"t");const Ht=function(t){this.message=t,this.name="CommandException"},Di=function(t){ot=t,console.log("binding instance")},Be=function(){if(at===null)throw new Ht("unknow namespace, namespace not set?")},Ii=function(t){at=t,Kt=t},ki=function(t){return Be(),Dn(t,at)},xi=function(t){if(typeof t=="object")t.command_id=ki(JSON.stringify(t)),t.request_id=Kt;else throw new Ht("input should be an object")},st=function(t){xi(t),ot.sendMessage(JSON.stringify(t))},Ni=function(){Be();const t={command:"getCams",timestamp:Date.now()};st(t)},Yt=function(t=null){if(!t)return;Be();const e={command:"switchCam",timestamp:Date.now(),payload:t};st(e)},Ri=function(t=null){if(!t)return;Be();const e={command:"ping",timestamp:Date.now(),payload:t};st(e)},P={onProgressUpdated:n=>{C(n,k.info),W.emit("progressUpdated",n)},onRemoteDataMessage:n=>{const t=JSON.parse(n.data),e=t.command;xe.rtcCommandCallBacks[e]&&typeof xe.rtcCommandCallBacks[e]=="function"&&xe.rtcCommandCallBacks[e](t)},onConnectionStateChange:(n,t)=>{n=="connected"&&W.emit("progressUpdated","peer connected")},onStateReport:n=>{}};let Ft=null,Qe={last:null,lastMsgCode:null,missingCount:0},E=fr,ve=null,Bt="initial",Se=!1,Ce=!1,Qt=null,z=[],I=null,ct=[],j={},_e=null,ze=null,zt=null,Gt=null,ne=null,re={currentIndex:0,cams:[]};const Pi=function(n){var i,a;const t="viewer",e=E.liveSetting.widescreen?{width:{ideal:1280},height:{ideal:720}}:{width:{ideal:640},height:{ideal:480}};switch(ne={video:E.liveSetting[t].sendVideo?e:!1,audio:E.liveSetting[t].sendAudio},zt=V.requestId,Gt=A.signalingName,E.clientId=V.requestId,E.role=A.role,E.region=L==null?void 0:L.region,E.region=V.region,E.channelName=L==null?void 0:L.channelName,E.accessKeyId=L==null?void 0:L.kvs_accessId,E.secretAccessKey=L==null?void 0:L.kvs_accessSecret,E.sessionToken=L==null?void 0:L.kvs_accessToken,E.autoStreaming=A.autoStreaming,E.liveSetting.openDataChannel=A.enableDataChannel,E.dataOnly=A.connectMode==="dataOnly",L!=null&&L.prefetched_configuration?(E.signaling_channel=L.prefetched_configuration.signaling_channel,E.ice_server_list=L.prefetched_configuration.ice_server_list,E.channel_arn=L.prefetched_configuration.channel_arn):(E.signaling_channel=null,E.ice_server_list=[],E.channel_arn="not ready"),A.connectMode){case"liveview":Se=!1,Ce=!0;break;case"playback":Se=!1,Ce=!0;break;case"p2p-audio_enable":Se=!0,Ce=!1;break;case"audio_call":ji(),Se=!0,Ce=!1,_e=new AudioContext,gainNode=_e.createGain();break;default:Se=!1,Ce=!1}return C(`Ready state:${j==null?void 0:j.readyState}`,k.info),(!j||j.readyState!==1)&&(j=new Xe.SignalingClient({channelARN:E.channel_arn,channelEndpoint:((i=E.signaling_channel)==null?void 0:i.WSS)??"not ready",clientId:E.clientId,role:E.role,region:E.region,credentials:{accessKeyId:E.accessKeyId,secretAccessKey:E.secretAccessKey,sessionToken:E.sessionToken}})),{configuration:{iceServers:E.ice_server_list,iceTransportPolicy:E.liveSetting.forceTURN?"relay":"all",channelARN:E.channel_arn,channelEndpoint:((a=E.signaling_channel)==null?void 0:a.WSS)??"not ready",clientId:E.clientId,role:E.role,region:E.region,wsLocal:!1,credentials:{accessKeyId:E.accessKeyId,secretAccessKey:E.secretAccessKey,sessionToken:E.sessionToken}},constraints:ne}},Li=async function(){var{configuration:n,constraints:t}=Pi();C(`default signal channel:${n.channelARN}`,k.info),(!j||j.readyState!==1)&&(j=new Xe.SignalingClient({...n})),Vi({configuration:n,constraints:t})},Ui=function(n,t,e){for(var r=n.split(`
`),i=-1,a=0;a<r.length;a++)if(r[a].indexOf("m="+t)===0){i=a;break}if(i===-1)return console.debug("Could not find the m line for",t),n;for(console.debug("Found the m line for",t,"at line",i),i++;r[i].indexOf("i=")===0||r[i].indexOf("c=")===0;)i++;if(r[i].indexOf("b")===0)return console.debug("Replaced b line at line",i),r[i]="b=AS:"+e,r.join(`
`);console.debug("Adding new b line before line",i);var o=r.slice(0,i);return o.push("b=AS:"+e),o=o.concat(r.slice(i,r.length)),o.join(`
`)},Xt=function(n){if(n.iceServers.length===0)return;const{liveSetting:t}=E,{role:e}=A,r=({type:i,messagePayload:a=null})=>{switch(i){case"session":a.is_stable&&(P.onProgressUpdated("session stable"),W.emit("session stable"));break}};try{P.onProgressUpdated("init peer connection"),I=new RTCPeerConnection(n),C("registering to start stat collection",k.info),ze=setInterval(()=>{I&&I.getStats().then(i=>{typeof P.onStateReport=="function"&&P.onStateReport.call(null,i)})},1e3),A.enableDataChannel&&(z.local=I.createDataChannel("kvsDataChannel"),z.local.onmessage=i=>{const a=JSON.parse(i.data),{action:o}=a;if(o)switch(o){case"SDP_OFFER":{const s=JSON.stringify({messagePayload:a.messagePayload,messageType:o});j.onMessage({data:s});break}case"cam_msg":r(a);break;default:break}else typeof P.onRemoteDataMessage=="function"&&P.onRemoteDataMessage(i)},I.ondatachannel=i=>{z.remote=i.channel,C(`[${e}] remote open data channel, wait for stable:delay`),setTimeout(()=>{P.onProgressUpdated("remote data channel opened")},1e3),setTimeout(()=>{Ii(V.requestId),xe.getCams({},a=>{var o,s;if(a.code==="D_S00000"&&a.command==="getCams"){let u=(o=a.payload)==null?void 0:o.cams;u&&!Number.isNaN((s=u[0])==null?void 0:s.id)&&(u=a.payload.cams.map(d=>{switch(d.id){case"1":return{id:"C_F"};case"2":return{id:"C_I"};case"4":return{id:"C_R"};default:return{id:d.id}}}).filter(d=>d));const[l]=u;re.cams=u,W.emit("getCams",re),Ft=setInterval(()=>{console.log("send heartbeat");const d=setTimeout(()=>{Qe.missingCount+=1},2e3);xe.ping({},h=>{clearTimeout(d),Qe.lastMsgCode=h.code,Qe.last=h.payload.pong,Qe.missingCount=0})},1e4)}})},10)},z.onclose=()=>{C(`[${e}] data channel closed`)})}catch(i){I=null,C(i),typeof P.onPeerCreateFail=="function"&&P.onPeerCreateFail.call(null)}I.addEventListener("icecandidateerror",i=>{C("%cCandidateErr",k.debug)}),I.addEventListener("icecandidate",({candidate:i})=>{t.useTrickleICE&&(i?j.sendIceCandidate(i):i||C("empty candidate, skip",k.notice),typeof P.onCandidate=="function"&&P.onCandidate(i))}),I.addEventListener("track",i=>{C(`[${e}] Received remote track:${i.track.kind}(${i.track.id})`,k.info)}),I.addEventListener("addstream",i=>{C("add new stream",i)}),I.addEventListener("iceconnectionstatechange",i=>{let a={disconnected:k.error};C(`[${e}] iceconnectionstatechange:${I.iceConnectionState}`,a[I.iceConnectionState]??k.info)}),I.onconnectionstatechange=qi.bind(this),I.onnegotiationneeded=Ki.bind(this),I.onicegatheringstatechange=Hi.bind(this),I.onsignalingstatechange=Yi.bind(this),I.ontrack=$i.bind(this)},Jt=async function(n){var a,o,s;if(n?(C("[VIEWER] setup server lists",k.info),I.iceServers=n.ice_server_list):C("data is null, fallback to compatible mode",k.notice),A.connectMode==="audio_call"){if(!A.capability.audio){P.onProgressUpdated("fail to get system input devices");return}try{P.onProgressUpdated("requiring the permission of microphone"),ve=await navigator.mediaDevices.getUserMedia({video:!1,audio:!0})}catch(l){C(l),P.onProgressUpdated("fail to require the permission of microphone");return}P.onProgressUpdated("required the permission of microphone"),C(ve);const u=ve.getAudioTracks();if(C(u),u.length>0){var t=_e.createMediaStreamSource(ve),e=_e.createMediaStreamDestination();t.connect(gainNode),gainNode.connect(e),gainNode.gain.value=1,I.addStream(e.stream)}}C("[VIEWER] Creating SDP offer",k.info),C(`[VIEWER] connect mode:${A.connectMode}`,k.info),C(`[VIEWER] capability:${A.capability}`,k.info);const r=Wi();let i=await I.createOffer(r);C("offerConstraints:"),C("SDP offer created"),(a=ne==null?void 0:ne.bandwidth)!=null&&a.video&&((o=ne==null?void 0:ne.bandwidth)==null?void 0:o.video)!=-1&&(i.sdp=Ui(i.sdp,"video",(s=ne==null?void 0:ne.bandwidth)==null?void 0:s.video)),P.onProgressUpdated("create sdp offer"),await I.setLocalDescription(i),j.sendSdpOffer(I.localDescription),C("localDescription:",I.localDescription)},ji=async function(){C("----------get media device----------"),await navigator.mediaDevices.enumerateDevices().then(n=>{C(n),n.forEach(t=>{t.kind==="videoinput"&&(A.capability.video=!0),t.kind==="audioinput"&&(A.capability.audio=!0)})}).catch(n=>{C(`${n.name}: ${n.message}`,k.error)}),C(A.capability)},$i=async function(t){var r;const{role:e}=A;if(C(`[${e}] remote tracks received `,k.info),t.track.kind==="audio"){const i=document.querySelector("#rtc_audio"),[a]=t.streams,o=t.track;i.srcObject=a,o.enabled=!0}if(t.track.kind==="video"){if((r=A.remoteView)!=null&&r.srcObject)return;const[i]=t.streams,a=t.track;A.connectMode==="audio_call"&&(a.enabled=!1),A.remoteView&&(A.remoteView.srcObject=i)}},Wi=function(){return{offerToReceiveAudio:Se,offerToReceiveVideo:Ce,voiceActivityDetection:!1}},Vi=async function({configuration:n,constraints:t}){C("Starting viewer",k.debug);const{role:e}=A;j.on("open",async()=>{n.iceServers.length!==0&&Xt(n),C(`[${e}] Connected to signaling service`),P.onProgressUpdated("signaling channel connected"),(I==null?void 0:I.getConfiguration().iceServers.length)>0&&Jt()}),j.on("sdpAnswer",async s=>{C(`[${e}] Received SDP answer`,s);try{await I.setRemoteDescription(s),P.onProgressUpdated("received sdp answer")}catch(u){C(u)}}),j.on("sdpOffer",async s=>{C(s),C("received remote offer",k.info),await I.setRemoteDescription(s)}),C("add master ready listener",k.debug),j.on("masterReady",async s=>{C("master ready received",k.info),P.onProgressUpdated("got master connected"),n.iceServers=s.ice_server_list,Xt(n),Jt(s)}),j.on("iceCandidate",s=>{C(`[${e}] Received ICE candidate`,k.info);try{I.addIceCandidate(s),s||C("remote peer generated all candidates",k.info)}catch(u){C(u)}}),j.on("close",()=>{C(`[${e}] Disconnected from signaling channel`,k.notice)}),j.on("error",s=>{console.error(`[${e}] Signaling client error: `,s)}),C("---------opening signaling---------",k.info);let r="wss",i="dev-ws-connect.visionmaxfleet.com",a="",o=null;n.wsLocal&&(r="ws",i="127.0.0.1:28591",a="&Custom-Connection-Flag=yes"),A.signalingState!==null&&(o=`${r}://${i}/signaling/${Gt}?request_id=${zt}${a}`,V.request_id&&(o+=`&viewer_request_id=${V.request_id}`)),j.open(o)},qi=function(n){var r;const{role:t}=A;let e=`unknown case:${(r=n.target)==null?void 0:r.connectionState}`;switch(n.target.connectionState){case"connecting":e="internal:peer connecting";break;case"connected":e="internal:peer connection fully connected",Di({sendMessage:Fi}),W.emit("peer connected");break;case"disconnected":case"failed":e=`internal:transports has terminated unexpectedly or in an error,state:${n.target.connectionState}`,W.emit("connection lost");break;case"closed":e="internal:peer connection closed";break;default:C("unknown case",k.debug);break}C(e,k.debug),P.onProgressUpdated("connectionStateChange",e),Qt=n.target.connectionState,typeof P.onConnectionStateChange=="function"&&(C(`[${t}] connectionState:${n.srcElement.connectionState} `,k.info),P.onConnectionStateChange.bind(this)(Qt,n))},Ki=function(n){const{role:t}=A;C(`[${t}] emit onnegotiationneeded `,k.notice),typeof P.onnegotiationneeded=="function"&&P.onnegotiationneeded.bind(this)(n)},Hi=function(n,t,e){const{role:r}=E,i=n.target;let a="Unknown";switch(i.iceGatheringState){case"new":case"complete":a="Idle";break;case"gathering":a="Determining route";break;default:a="unknown state";break}C(`[${r}] ice gatheringStatus:${a} `,k.info)},Yi=function(n){const{role:t}=E,e=n.target;C(`[${t}] signal state:${e.signalingState} `)},Fi=function(n){const{role:t}=A;if(t==="VIEWER")try{z.remote.send(n)}catch(e){C(n),C("[VIEWER] Send DataChannel: ",e.toString())}else Object.keys(ct).forEach(e=>{try{ct[e]&&ct[e].remote.send(n)}catch(r){C("[MASTER] Send DataChannel: ",r.toString())}})},Bi=async function(){const{role:n}=A;if(C(`[${n}] Stopping viewer connection`),j&&(j.close(),j=null,A.signalingState=0),z!=null&&z.local&&z.local.close(),z!=null&&z.remote&&z.remote.close(),I&&(clearInterval(ze),clearInterval(Ft),I.close(),I=null),ve&&(ve.getTracks().forEach(t=>t.stop()),ve=null),ze&&(ze=null),A.remoteView&&(A.remoteView.srcObject=null),A.connectMode==="audio_call")try{gainNode=null,_e.close(),_e=null}catch(t){C(t.message)}Bt="initial",C("closing viewer and reset")},Qi=async function(n,t,e=null){const r=n??zi(),i=re.currentIndex;if(n===re.cams[i].id){e({code:"skip",msg:"skip due to the target cam is same"});return}if(n&&!this.availableCams.cams.find((o,s)=>o.id===n?(this.availableCams.currentIndex=s,!0):!1))return!1;const a={id:`${r}`};Yt(a)},zi=function(){return re.currentIndex+1<re.cams.length?(re.currentIndex+=1,re.cams[re.currentIndex].id):(re.currentIndex=0,re.cams[0].id)},G={startLive:Li,get signalingClient(){return j},get selectedCandidatePairId(){return Bt},stopViewer:Bi,toSwitchCam:Qi,callBacks:P};let Z=null,L=null;const Gi=function(n,t){let e="";Z&&Z.isConnected()||([e]=$.credential.clientId,Z=new window.Paho.MQTT.Client(n,e));const r={onSuccess:Xi,useSSL:!0,timeout:3,mqttVersion:4,onFailure(i){console.log("connect to broker fail"),console.log(i)}};Z.connect(r)},Xi=function(){G.callBacks.onProgressUpdated("broker connected"),$.topics.forEach(n=>{Z.subscribe(n),console.log(n)}),G.callBacks.onProgressUpdated("subscribe to topics"),Z.deviceCId=le.deviceCId,Z.requestId=le.requestId,Z.onConnectionLost=n=>{console.log("broker disconnnected",n)},Z.onMessageArrived=Ji},Ji=function(n){let t=null;n.destinationName.split("/");try{t=JSON.parse(n.payloadString)}catch{t=null,console.log("ignore the bad payload")}if(t){if(n.destinationName.indexOf(`viewer/credential/${V.requestId}`)!==-1){if(G.selectedCandidatePairId!=="initial"){console.log("got duplicated kvs payload");return}G.callBacks.onProgressUpdated("credential for connecting to signaling channel"),L=t;return}if(n.destinationName.indexOf("viewer/voip")!==-1){console.log("voip credential"),L=t,startLiveVewSession(t);return}if(n.destinationName.indexOf(`viewer/${V.requestId}`)!==-1&&t.type==="device_state"){const e=()=>{L?G.startLive():setTimeout(()=>{e()},50)};if(t.data.state==="master connected")G.callBacks.onProgressUpdated("got master connected"),(["liveview","playback"].includes(A.connectMode)||A.connectMode==="audio_call"&&A.callDirection==="out")&&e();else if(t.data.state==="device-busy"){let r="busy";t.data.flag==="restarting"&&(r="busyAndRetry"),console.log(`device state:${r}`)}else t.data.state==="device-failed"||(t.data.state==="server-request"?console.log("device disconnected by server request"):t.data.state==="ping-pong-timeout"?console.log("the app gone away due to heartbeat failure"):t.data.state==="by-new-viewer-disconnected:device_call_out"||t.data.state)}else console.log(`ingnoring:${t.request_id}`)}},Zi=function(n){A.signalingState===null&&G.signalingClient.close(),$.topics.length||($.topics.push(`${$.root}/viewer/credential/${V.requestId}`),$.topics.push(`${$.root}/viewer/${V.requestId}`),$.topics.push(`${$.prefix}/${V.deviceCId}/live-view`)),le.requestId=n.request_id,n.constraints={bandwidth:{video:le.liveviewConfiguration.bit_rate}},le.options=n,le.exposed.requestId=n.request_id,le.exposed.deviceCId=le.deviceCId,Gi(pn(V.iotDeviceGateway,$.region))},ea=function(){Z&&Z.isConnected()&&Z.disconnect(),L=null,$.credential=null,$.prefix=null,$.region=null,$.root=null,$.clientId=[],$.topics=[]},Ae={"send initial liveview":!1,"request authorized":!1,"init peer connection":!1,"signaling channel connected":!1,"received sdp answer":!1,"peer connected":!1},Ne={"send initial liveview":!1,"credential for connecting to broker received":!1,"connecting to broker":!1,"broker connected":!1,"subscribe to topics":!1,"credential for connecting to signaling channel":!1,"signaling channel connected":!1,"got master connected":!1,"create sdp offer":!1,"received sdp answer":!1,"init peer connection":!1,"peer connected":!1};let Zt={};const lt={updateProgressStages:(n,t)=>{if(C(`progress update:${n}`,k.debug),n in Ne?(Ne[n]=!0,ft()):Zt[n]=!0,n in Ae){let e=0,r=0,i=0;Ae[n]=!0,Object.keys(Ae).forEach(a=>{Ae[a]&&(e+=1),r+=1}),i=Math.ceil(e/r*100,10),W.emit("progressStatusUpdated",i),i===100&&nn()}},get stages(){return Ne},resetProgressStages:()=>{Object.keys(Ae).forEach(n=>{Ae[n]=!1}),Object.keys(Ne).forEach(n=>{Ne[n]=!1}),Zt={}}};W.on("progressUpdated",lt.updateProgressStages);let ut=null,en=null,tn="liveview",Ee=null,ta="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4NCiAgICA8cGF0aA0KICAgICAgZD0iTTAgNEMwIDEuNzkwODYgMS43OTA4NiAwIDQgMEgzNkMzOC4yMDkxIDAgNDAgMS43OTA4NiA0MCA0VjM2QzQwIDM4LjIwOTEgMzguMjA5MSA0MCAzNiA0MEg0QzEuNzkwODYgNDAgMCAzOC4yMDkxIDAgMzZWNFoiDQogICAgICBmaWxsPSJibGFjayINCiAgICAgIGZpbGwtb3BhY2l0eT0iMC42Ig0KICAgIC8+DQogICAgPHBhdGgNCiAgICAgIGZpbGwtcnVsZT0iZXZlbm9kZCINCiAgICAgIGNsaXAtcnVsZT0iZXZlbm9kZCINCiAgICAgIGQ9Ik0yOC4zOTg5IDEyLjY0OTRIMjUuMDcwM0wyMy4xNDg3IDEwLjU0OTNIMTYuODQ4NEwxNC45MjY4IDEyLjY0OTRIMTEuNTk4MUMxMC40NDMxIDEyLjY0OTQgOS40OTgwNSAxMy41OTQ1IDkuNDk4MDUgMTQuNzQ5NVYyNy4zNTAxQzkuNDk4MDUgMjguNTA1MSAxMC40NDMxIDI5LjQ1MDIgMTEuNTk4MSAyOS40NTAySDI4LjM5ODlDMjkuNTU0IDI5LjQ1MDIgMzAuNDk5IDI4LjUwNTEgMzAuNDk5IDI3LjM1MDFWMTQuNzQ5NUMzMC40OTkgMTMuNTk0NSAyOS41NTQgMTIuNjQ5NCAyOC4zOTg5IDEyLjY0OTRaTTI4LjM5ODkgMjcuMzUwMUgxMS41OTgxVjE0Ljc0OTVIMTQuOTI2OEgxNS44NTA4TDE2LjQ3MDQgMTQuMDY3TDE3Ljc3MjQgMTIuNjQ5NEgyMi4yMjQ2TDIzLjUyNjcgMTQuMDY3TDI0LjE0NjIgMTQuNzQ5NUgyNS4wNzAzSDI4LjM5ODlWMjcuMzUwMVpNMTkuOTk4NCAyNS4yNTAyQzE3LjY3NzggMjUuMjUwMiAxNS43OTgyIDIzLjM3MDYgMTUuNzk4MiAyMS4wNUgxNy44OTgzTDE1LjI3MzIgMTguNDI0OUwxMi42NDgxIDIxLjA1SDE0Ljc0ODJDMTQuNzQ4MiAyMy45NDgyIDE3LjEwMDMgMjYuMzAwMyAxOS45OTg0IDI2LjMwMDNDMjAuOTAxNSAyNi4zMDAzIDIxLjczMSAyNi4wNDgzIDIyLjQ3NjYgMjUuNjQ5MkwyMS42OTk1IDI0Ljg3MjJDMjEuMTg1IDI1LjExMzcgMjAuNjA3NSAyNS4yNTAyIDE5Ljk5ODQgMjUuMjUwMlpNMTcuNTIwOSAxNi40NTA1QzE4LjI2NjQgMTYuMDUxNSAxOS4wOTU5IDE1Ljc5OTUgMTkuOTk5IDE1Ljc5OTVDMjIuODk3MSAxNS43OTk1IDI1LjI0OTIgMTguMTUxNiAyNS4yNDkyIDIxLjA0OTdIMjcuMzQ5M0wyNC43MjQyIDIzLjY3NDlMMjIuMDk5MSAyMS4wNDk3SDI0LjE5OTJDMjQuMTk5MiAxOC43MjkxIDIyLjMxOTYgMTYuODQ5NSAxOS45OTkgMTYuODQ5NUMxOS4zOSAxNi44NDk1IDE4LjgxMjQgMTYuOTg2IDE4LjI5NzkgMTcuMjE3MUwxNy41MjA5IDE2LjQ1MDVaIg0KICAgICAgZmlsbD0id2hpdGUiDQogICAgLz4NCiAgPC9zdmc+",dt=0,Re=null;const se=document.createElement("img"),ie=document.createElement("div"),Pe=`${cn}.${ln}.${un}`,na=function(n){return ut=hn(n.env,n.apiKey),en=n.clientAccessToken,Ee=document.querySelector("#rtc_liveview"),A.remoteView=Ee,A.switchCameraButton=n.feature.enableDefaultCameraSwitchButton,A.switchCameraButton&&W.on("getCams",t=>{C(t);const e=Ee.parentNode;se.src=ta,se.style.bottom="10px",se.style.right="10px",se.style.position="absolute",se.style.zIndex=1e3,se.style.cursor="pointer",e.insertBefore(se,Ee),se.addEventListener("click",rn)}),W.on("session stable",t=>{C(t);const e=Ee.parentNode;ie.style.bottom="10px",ie.style.left="10px",ie.style.position="absolute",ie.style.zIndex=1e3,ie.style.textAlign="left",e.insertBefore(ie,Ee),A.is_vmRTC_VersionUnsupported?(ie.style.color="red",ie.innerHTML=`vmRTC ${Pe} is not supported.<br/>Please consider updating.`):A.is_vmRTC_VersionDeprecated&&(ie.style.color="yellow",ie.innerHTML=`vmRTC ${Pe} is deprecated.<br/>Please consider updating.`,setTimeout(()=>{ie.innerHTML=""},6e3))}),this},ra=function(){dt++,dt>=A.maxFailCounter&&(W.emit("connect fail",lt.stages),ft(),clearInterval(Re))},ft=function(){dt=0},nn=function(){clearInterval(Re),ft()},ia=async function(n,t="liveview"){tn=t,A.connectMode=tn;const e=Tn();V.deviceCId=n,V.requestId=e,V.vid=e;let r={"X-Api-Key":ut.apiKey,Authorization:`Bearer ${en}`},i=`cid=${n}&vid=${e}&mode=${t}`;W.emit("init.connect"),Re=setInterval(()=>{ra()},1e3),t!=="liveview"&&state.callDirection,W.emit("progressUpdated","send initial liveview"),(async a=>{aa()})(),t==="liveview"&&fetch(`https://${ut.host}/${In.initLiveView}?${i}&lib=${Pe}`,{headers:r}).then(a=>{if(!a.ok)throw W.emit("error.init.connect"),new Error(`HTTP error! Status: ${a.status}`);return a.json()}).then(a=>{var o,s,u,l,d,h;(o=a.data)!=null&&o.credentials?(G.callBacks.onProgressUpdated("request authorized"),G.callBacks.onProgressUpdated("credential for connecting to broker received"),$.credential={accessKeyId:a.data.credentials.key,secretAccessKey:a.data.credentials.secret,sessionToken:a.data.credentials.token,clientId:a.data.credentials.client_id,region:a.data.region},$.prefix=a.data.prefix,$.region=a.data.region,$.root=a.data.root,Object.values(a.data.credentials.topics).forEach(v=>{$.topics.push(v)}),$.topics.push(`${a.data.prefix}/${a.data.cid}/live-view`),ge.credential.accessKeyId=a.data.credentials.key,ge.credential.secretAccessKey=a.data.credentials.secret,ge.credential.sessionToken=a.data.credentials.token,ge.region=a.data.region,V.iotDeviceGateway=a.data.deviceGateway,V.deviceCId=n,V.vid=e,V.requestId=a.data.request_id,A.signalingState=a.data.signaling_state,A.setting=a.data.liveview_setting,A.connectMode=t,A.signalingName=n,(u=(s=a.data)==null?void 0:s.libs)!=null&&u.rtc&&(A.is_vmRTC_VersionDeprecated=((l=a.data.libs.rtc[Pe])==null?void 0:l.deprecated)??!1,A.is_vmRTC_VersionUnsupported=((d=a.data.libs.rtc[Pe])==null?void 0:d.unsupported)??!1),G.callBacks.onProgressUpdated("connecting to broker"),Zi(a.data)):((h=a.data)==null?void 0:h.remaining_time)===0?(W.emit("err",yt("debug","no remaining time")),W.emit("err",yt("debug","no remaining time")),W.emit("initial.fail","no remaining time"),clearInterval(Re)):a.code!="S00000"&&(clearInterval(Re),W.emit("initial.fail","permission deny"))})},aa=function(n){G.startLive()},rn=function(){G.toSwitchCam()};return{...(function(){let n=this,t="liveview";return Object.assign(n,{setup:na,start:e=>{ia(e,t)},stop:()=>{var e;ea(),G.stopViewer(),lt.resetProgressStages(),(e=se.parentNode)==null||e.removeChild(se),nn()},addEventListener:(e,r)=>{W.on(e,r)},removeEventListener:(e,r)=>{W.removeListener(e,r)},nextCam:rn}),console.log("env:production"),n}).bind(new Object)()}});
