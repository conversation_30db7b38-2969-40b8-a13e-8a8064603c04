import React, { useEffect, useRef, useState } from 'react';

export interface Asset {
  id: string;
  deviceId: string;
  name: string;
  status: 'Online' | 'Offline' | 'Idle' | 'Maintenance';
  position: {
    lat: number;
    lng: number;
  };
  vehicleType?: 'truck' | 'van' | 'car' | 'other';
  licensePlate?: string | null;
  lastUpdate?: string;
  isOngoing?: boolean;
  speed?: number;
  [key: string]: any;
}

interface LiveFleetMapProps {
  assets: Asset[];
  selectedAssetId?: string;
  center?: { lat: number; lng: number };
  zoom?: number;
  onAssetClick?: (assetId: string) => void;
  className?: string;
}

declare global {
  interface Window {
    google: any;
    initMap: () => void;
  }
}

const defaultCenter = { lat: 39.8283, lng: -98.5795 };

export const LiveFleetMap: React.FC<LiveFleetMapProps> = ({
  assets,
  selectedAssetId,
  center = defaultCenter,
  zoom = 4,
  onAssetClick,
  className = '',
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const googleMapRef = useRef<any>(null);
  const markersRef = useRef<Map<string, any>>(new Map());
  const infoWindowRef = useRef<any>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedAsset, setSelectedAsset] = useState<Asset | null>(null);

  const apiKey = import.meta.env.VITE_APP_GOOGLE_MAPS_API_KEY;

  // Load Google Maps script
  useEffect(() => {
    console.log(assets);
    if (!apiKey) {
      setError('Google Maps API key is missing');
      return;
    }

    if (window.google) {
      setIsLoaded(true);
      return;
    }

    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=marker,geometry&v=weekly`;
    script.async = true;
    script.defer = true;
    
    script.onload = () => {
      console.log('✅ Google Maps API loaded');
      setIsLoaded(true);
    };
    
    script.onerror = () => {
      console.error('❌ Failed to load Google Maps API');
      setError('Failed to load Google Maps API');
    };

    document.head.appendChild(script);

    return () => {
      if (script.parentNode) {
        script.parentNode.removeChild(script);
      }
    };
  }, [apiKey]);

  // Initialize map
  useEffect(() => {
    if (!isLoaded || !mapRef.current || !window.google) return;

    try {
      console.log('🗺️ Initializing Google Map');
      
      const map = new window.google.maps.Map(mapRef.current, {
        center: center,
        zoom: zoom,
        mapId: 'DEMO_MAP_ID', // Required for AdvancedMarkerElement
        mapTypeId: 'roadmap',
        disableDefaultUI: false,
        zoomControl: true,
        streetViewControl: true,
        mapTypeControl: true,
        fullscreenControl: true,
        clickableIcons: false, // Prevent POI clicks from interfering
      });

      googleMapRef.current = map;

      // Initialize InfoWindow
      infoWindowRef.current = new window.google.maps.InfoWindow({
        disableAutoPan: false,
        maxWidth: 300,
        pixelOffset: new window.google.maps.Size(0, -10)
      });

      // Add map click listener to close InfoWindow
      map.addListener('click', (event) => {
        console.log('🗺️ Map clicked - closing InfoWindow');
        if (infoWindowRef.current) {
          infoWindowRef.current.close();
        }
        setSelectedAsset(null);
        if (onAssetClick) {
          onAssetClick('');
        }
      });

      console.log('✅ Google Map initialized successfully');
    } catch (err) {
      console.error('❌ Error initializing map:', err);
      setError(`Error initializing map: ${err}`);
    }
  }, [isLoaded, center, zoom]);

  // Create marker icon
  const createMarkerIcon = (asset: Asset) => {
    const statusColors = {
      'online': '#22c55e',
      'offline': '#ef4444',
      'idle': '#eab308',
      'maintenance': '#3b82f6'
    };

    const color = statusColors[asset.status.toLowerCase()] || '#ef4444';

    // Create a custom pin element
    const pinElement = document.createElement('div');
    pinElement.style.width = '20px';
    pinElement.style.height = '20px';
    pinElement.style.backgroundColor = color;
    pinElement.style.border = '3px solid white';
    pinElement.style.borderRadius = '50%';
    pinElement.style.boxShadow = '0 2px 6px rgba(0,0,0,0.3)';
    pinElement.style.cursor = 'pointer';
    
    // Add status letter
    pinElement.innerHTML = `<div style="
      color: white; 
      font-size: 10px; 
      font-weight: bold; 
      text-align: center; 
      line-height: 14px;
      text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
    ">${asset.status.charAt(0)}</div>`;

    return pinElement;
  };

  // Create and manage markers
  useEffect(() => {
    if (!googleMapRef.current || !window.google || !window.google.maps.marker) return;

    console.log(`🚗 Creating markers for ${assets.length} assets`);

    // Clear existing markers
    markersRef.current.forEach(marker => {
      if (marker && marker.map) {
        marker.map = null;
      }
    });
    markersRef.current.clear();

    // Filter valid assets
    const validAssets = assets.filter(asset => {
      const lat = asset.position?.lat;
      const lng = asset.position?.lng;
      const isValid = lat && lng && !isNaN(lat) && !isNaN(lng) && Math.abs(lat) > 0.001 && Math.abs(lng) > 0.001;
      
      if (!isValid) {
        console.log(`❌ Invalid coordinates for ${asset.deviceId}:`, asset.position);
      }
      
      return isValid;
    });

    console.log(`📍 Creating ${validAssets.length} valid markers`);

    // Create new markers using AdvancedMarkerElement
    validAssets.forEach((asset, index) => {
      try {
        console.log(`Creating marker ${index + 1}/${validAssets.length} for ${asset.deviceId}`);

        const markerElement = new window.google.maps.marker.AdvancedMarkerElement({
          map: googleMapRef.current,
          position: asset.position,
          content: createMarkerIcon(asset),
          title: `${asset.name || asset.deviceId} - ${asset.status}`,
          gmpClickable: true,
        });

        // Add click listener with proper event handling
        markerElement.addListener('click', (event) => {
          console.log(`🖱️ Marker clicked: ${asset.deviceId}`);
          handleMarkerClick(asset, markerElement, event);
        });

        markersRef.current.set(asset.id, markerElement);
        console.log(`✅ Marker created for ${asset.deviceId}`);
      } catch (err) {
        console.error(`❌ Error creating marker for ${asset.deviceId}:`, err);
        
        // Fallback to old Marker API if AdvancedMarkerElement fails
        try {
          const fallbackMarker = new window.google.maps.Marker({
            map: googleMapRef.current,
            position: asset.position,
            title: `${asset.name || asset.deviceId} - ${asset.status}`,
            icon: {
              url: `https://maps.google.com/mapfiles/ms/icons/${
                asset.status.toLowerCase() === 'online' ? 'green' :
                asset.status.toLowerCase() === 'idle' ? 'yellow' :
                asset.status.toLowerCase() === 'maintenance' ? 'blue' : 'red'
              }-dot.png`,
              scaledSize: new window.google.maps.Size(32, 32),
            }
          });

          fallbackMarker.addListener('click', (event) => {
            // Stop event propagation
            if (event && event.stop) event.stop();
            handleMarkerClick(asset, fallbackMarker, event);
          });

          markersRef.current.set(asset.id, fallbackMarker);
          console.log(`✅ Fallback marker created for ${asset.deviceId}`);
        } catch (fallbackErr) {
          console.error(`❌ Fallback marker also failed for ${asset.deviceId}:`, fallbackErr);
        }
      }
    });

  }, [assets, isLoaded]);

  // Handle marker click
  const handleMarkerClick = (asset: Asset, marker: any, event?: any) => {
    console.log(`🖱️ Handling click for ${asset.deviceId}`);
    
    // Stop event propagation to prevent map click
    if (event) {
      event.stop();
      event.domEvent?.stopPropagation();
    }
    
    setSelectedAsset(asset);
    
    if (onAssetClick) {
      onAssetClick(asset.id);
    }

    // Create InfoWindow content
    const infoContent = `
      <div style="padding: 16px; min-width: 200px; font-family: Arial, sans-serif;" onclick="event.stopPropagation();">
        <h3 style="margin: 0 0 12px 0; font-size: 18px; font-weight: bold; color: #333;">
          ${asset.name || asset.deviceId}
        </h3>
        
        <div style="margin-bottom: 8px;">
          <strong>Status:</strong> 
          <span style="
            padding: 4px 8px; 
            border-radius: 12px; 
            font-size: 12px; 
            background-color: ${
              asset.status.toLowerCase() === 'online' ? '#dcfce7' :
              asset.status.toLowerCase() === 'idle' ? '#fef3c7' :
              asset.status.toLowerCase() === 'maintenance' ? '#dbeafe' : '#fee2e2'
            }; 
            color: ${
              asset.status.toLowerCase() === 'online' ? '#166534' :
              asset.status.toLowerCase() === 'idle' ? '#92400e' :
              asset.status.toLowerCase() === 'maintenance' ? '#1e40af' : '#991b1b'
            };
          ">${asset.status}</span>
        </div>
        
        <div style="margin-bottom: 8px;">
          <strong>Device ID:</strong> ${asset.deviceId}
        </div>
        
        ${asset.licensePlate ? `
          <div style="margin-bottom: 8px;">
            <strong>License:</strong> ${asset.licensePlate}
          </div>
        ` : ''}
        
        ${asset.vehicleType ? `
          <div style="margin-bottom: 8px;">
            <strong>Type:</strong> ${asset.vehicleType}
          </div>
        ` : ''}
        
        ${asset.isOngoing ? `
          <div style="margin-bottom: 12px; color: #166534; font-weight: bold;">
            🚛 Currently on trip
          </div>
        ` : ''}
        
        <div style="margin-top: 12px; padding-top: 12px; border-top: 1px solid #e5e7eb;">
          <a href="/devices" 
             style="
               display: block; 
               text-align: center; 
               padding: 8px 16px; 
               background-color: #3b82f6; 
               color: white; 
               text-decoration: none; 
               border-radius: 6px; 
               font-size: 14px;
             "
             onclick="event.stopPropagation();">
            📊 View Details
          </a>
        </div>
      </div>
    `;

    // Show InfoWindow with delay to ensure proper rendering
    setTimeout(() => {
      if (infoWindowRef.current) {
        infoWindowRef.current.setContent(infoContent);
        infoWindowRef.current.open(googleMapRef.current, marker);
        console.log(`✅ InfoWindow opened for ${asset.deviceId}`);
      }
    }, 100);
  };

  // Handle external selection
  useEffect(() => {
    if (selectedAssetId && googleMapRef.current) {
      const asset = assets.find(a => a.id === selectedAssetId);
      if (asset) {
        const marker = markersRef.current.get(asset.id);
        if (marker) {
          handleMarkerClick(asset, marker);
          googleMapRef.current.panTo(asset.position);
          googleMapRef.current.setZoom(15);
        }
      }
    }
  }, [selectedAssetId, assets]);

  // Fit bounds to show all markers
  const fitBounds = () => {
    if (!googleMapRef.current || assets.length === 0) return;

    const bounds = new window.google.maps.LatLngBounds();
    const validAssets = assets.filter(asset => 
      asset.position?.lat && asset.position?.lng && 
      !isNaN(asset.position.lat) && !isNaN(asset.position.lng)
    );

    validAssets.forEach(asset => {
      bounds.extend(asset.position);
    });

    if (validAssets.length > 0) {
      googleMapRef.current.fitBounds(bounds);
    }
  };

  // Error state
  if (error) {
    return (
      <div className={`flex items-center justify-center h-full bg-red-50 border-2 border-red-300 rounded ${className}`}>
        <div className="text-center p-6">
          <div className="text-red-600 text-xl font-bold mb-2">❌ Google Maps Error</div>
          <div className="text-red-500 mb-2">{error}</div>
          {!apiKey && (
            <div className="text-gray-600 text-sm">
              <div>Please add your Google Maps API key to your .env file:</div>
              <div className="font-mono bg-gray-100 p-2 mt-2 rounded">
                VITE_APP_GOOGLE_MAPS_API_KEY=your_api_key_here
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Loading state
  if (!isLoaded) {
    return (
      <div className={`flex items-center justify-center h-full bg-blue-50 border-2 border-blue-300 rounded ${className}`}>
        <div className="text-center p-6">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <div className="text-blue-600 text-lg font-semibold">Loading Google Maps...</div>
          <div className="text-blue-500 text-sm mt-2">Using Modern AdvancedMarkerElement API</div>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative h-full ${className}`}>
      <div ref={mapRef} className="w-full h-full" />
      
      {/* Map controls */}
      <div className="absolute top-14 left-3 bg-white rounded-lg shadow-lg p-3">
        <div className="space-y-2">
          <button
            onClick={fitBounds}
            className="w-full px-3 py-2 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            🎯 Fit All Vehicles
          </button>
          
          {selectedAsset && (
            <button
              onClick={() => {
                setSelectedAsset(null);
                if (infoWindowRef.current) {
                  infoWindowRef.current.close();
                }
                if (onAssetClick) {
                  onAssetClick('');
                }
              }}
              className="w-full px-3 py-2 text-sm bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
            >
              ❌ Clear Selection
            </button>
          )}
        </div>
      </div>
      
      {/* Status legend */}
      {/* <div className="absolute bottom-4 right-4 bg-white rounded-lg shadow-lg p-3 max-w-xs">
        <div className="space-y-2">
          <div className="text-sm font-semibold text-gray-700 border-b pb-2">
            🚛 Fleet Status ({assets.length} vehicles)
          </div>
          
          <div className="grid grid-cols-2 gap-2 text-xs">
            {[
              { status: 'Online', color: 'bg-green-500', count: assets.filter(a => a.status.toLowerCase() === 'online').length },
              { status: 'Idle', color: 'bg-yellow-500', count: assets.filter(a => a.status.toLowerCase() === 'idle').length },
              { status: 'Offline', color: 'bg-red-500', count: assets.filter(a => a.status.toLowerCase() === 'offline').length },
              { status: 'Maintenance', color: 'bg-blue-500', count: assets.filter(a => a.status.toLowerCase() === 'maintenance').length }
            ].map(({ status, color, count }) => (
              <div key={status} className="flex items-center gap-2">
                <div className={`w-3 h-3 rounded-full ${color}`}></div>
                <span>{status} ({count})</span>
              </div>
            ))}
          </div>
          
          <div className="border-t pt-2 text-xs space-y-1">
            <div>🚛 On Trip: {assets.filter(a => a.isOngoing).length}</div>
            <div>🏃‍♂️ Moving: {assets.filter(a => a.speed && a.speed > 0).length}</div>
          </div>
        </div>
      </div> */}
      
      {/* Modern API indicator */}
      <div className="absolute top-4 right-4 bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium">
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
          <span>Live</span>
        </div>
      </div>
    </div>
  );
};

export default LiveFleetMap;