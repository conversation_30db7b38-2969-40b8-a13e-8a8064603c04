# Production Environment Variables for DashFleet UI
# These values match exactly what's hardcoded in your application code

# API Configuration (IMPORTANT: Your code uses VITE_APP_API_BASE_URL)
# Fallback value from src/api/apiClient.ts: 'https://api.visionmaxfleet.com/V2'
# Fallback value from src/api/axiosDefault.ts: 'https://api.visionmaxfleet.com/V2'
# Hardcoded in src/app/app.tsx: 'https://api.visionmaxfleet.com'
# Hardcoded in src/features/configuration/ConfigurationPage.tsx: 'https://api.visionmaxfleet.com'
# Hardcoded in src/features/dashboard/Dashboard.tsx: 'https://api.visionmaxfleet.com/'
VITE_APP_API_BASE_URL=https://api.visionmaxfleet.com

# API Key (hardcoded in your axios configs)
# From src/api/apiClient.ts and src/api/axiosDefault.ts
VITE_API_KEY=WnbpXF1lJ1vKePo1C5pCXbgVGbkwehE8wMvX5LV9ikQ

# Google Maps API Key (IMPORTANT: Your code uses VITE_APP_GOOGLE_MAPS_API_KEY)
# You need to replace this with your actual Google Maps API key
VITE_APP_GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here

# Stripe Configuration
# You need to replace this with your actual Stripe publishable key
VITE_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key_here

# WebSocket Configuration
# Hardcoded in src/features/devices/LiveViewModal.tsx: 'wss://dev-ws-connect.visionmaxfleet.com'
VITE_WEBSOCKET_URL=wss://dev-ws-connect.visionmaxfleet.com

# Application Configuration
VITE_APP_NAME=DashFleet UI
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=production

# Feature Flags (IMPORTANT: Your code uses these exact names)
VITE_APP_ENABLE_SOCKET_IO=true
VITE_APP_DEBUG_MODE=false

# API Timeout
VITE_API_TIMEOUT=10000
