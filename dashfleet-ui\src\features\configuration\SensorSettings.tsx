// src/components/configuration/SensorSettings.tsx
import React, { useState } from 'react';
import { Info } from 'lucide-react';

interface SensorSettingsProps {
  settings: Record<string, any>;
  onChange: (key: string, value: any) => void;
  modifiedValues: Record<string, any>;
}

const SensorSettings: React.FC<SensorSettingsProps> = ({
  settings,
  onChange,
  modifiedValues,
}) => {
  const [selectedFeature, setSelectedFeature] = useState('impact');

  // Helper function to get the current value (from modified values or original settings)
  const getValue = (key: string) => {
    return modifiedValues[key] !== undefined ? modifiedValues[key] : settings[key];
  };

  // Handle toggle changes
  const handleToggle = (key: string) => {
    onChange(key, !getValue(key));
  };

  // Handle slider changes
  const handleSliderChange = (key: string, value: number) => {
    onChange(key, value);
  };

  // Handle input number changes
  const handleNumberChange = (key: string, value: string) => {
    const numValue = parseInt(value, 10);
    if (!isNaN(numValue)) {
      onChange(key, numValue);
    }
  };

  // Handle select changes
  const handleSelectChange = (key: string, value: string) => {
    onChange(key, value);
  };

  return (
    <div className="grid grid-cols-12 gap-6">
      {/* Left side - toggles */}
      <div className="col-span-3 bg-gray-800 text-white rounded-lg">
        <div 
          className={`flex justify-between items-center p-4 cursor-pointer ${selectedFeature === 'impact' ? 'bg-blue-800' : ''}`}
          onClick={() => setSelectedFeature('impact')}
        >
          <span className="font-medium">Impact</span>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              className="sr-only peer"
              checked={getValue('impactEnable')}
              onChange={() => handleToggle('impactEnable')}
            />
            <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
          </label>
        </div>

        <div 
          className={`flex justify-between items-center p-4 cursor-pointer border-t border-gray-700 ${selectedFeature === 'harshDriving' ? 'bg-blue-800' : ''}`}
          onClick={() => setSelectedFeature('harshDriving')}
        >
          <span className="font-medium">Harsh Driving</span>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              className="sr-only peer"
              checked={getValue('harshEnable')}
              onChange={() => handleToggle('harshEnable')}
            />
            <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
          </label>
        </div>
      </div>

      {/* Right side - configuration */}
      <div className="col-span-9 bg-gray-900 text-white rounded-lg">
        {selectedFeature === 'impact' && renderImpactSettings()}
        {selectedFeature === 'harshDriving' && renderHarshDrivingSettings()}
      </div>
    </div>
  );

  function renderImpactSettings() {
    return (
        <div className="p-6 bg-white text-gray-800">
        <div className="mb-8">
          <h3 className="text-lg font-medium mb-2 text-gray-900">Event Trigger:</h3>
          <p className="text-sm text-gray-500">Set up the condition of triggering the event</p>
          
          <div className="mt-4 p-4 bg-gray-100 rounded-lg">
            <div className="mb-6">
              <h4 className="text-sm font-medium mb-2 text-gray-900">Event Type</h4>
              
              <div className="grid grid-cols-3 gap-4 mb-4">
                <div className="col-span-1">
                  <span className="block mb-2">Driving Impact</span>
                </div>
                <div className="col-span-2">
                  <div className="flex justify-between mb-1">
                    <div className="text-right">
                      <span className="text-sm font-medium">Sensitivity</span>
                    </div>
                  </div>
                  <div className="flex justify-between mb-1">
                    <div>
                      <span className="text-xs text-gray-500">Low<br />Less events</span>
                    </div>
                    <div className="text-right">
                      <span className="text-xs text-gray-500">High<br />More events</span>
                    </div>
                  </div>
                  
                  <input 
                    type="range" 
                    min="0" 
                    max="2" 
                    step="1"
                    value={getValue('impact_drivingSensitive')}
                    onChange={(e) => handleSliderChange('impact_drivingSensitive', parseInt(e.target.value, 10))}
                    className="w-full h-2 bg-gray-300 rounded-lg appearance-none cursor-pointer"
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-3 gap-4">
                <div className="col-span-1">
                  <span className="block mb-2">Parking Impact</span>
                </div>
                <div className="col-span-2">
                  <input 
                    type="range" 
                    min="0" 
                    max="2" 
                    step="1"
                    value={getValue('impact_parkingSensitive')}
                    onChange={(e) => handleSliderChange('impact_parkingSensitive', parseInt(e.target.value, 10))}
                    className="w-full h-2 bg-gray-300 rounded-lg appearance-none cursor-pointer"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
  
        <div className="mb-8">
          <h3 className="text-lg font-medium mb-2 text-gray-900">Event Recording:</h3>
          <p className="text-sm text-gray-500 mb-6">Set up the duration of the recorded video when the event is triggered, which will be captured by the device for reviewing.</p>
          
          <div className="flex items-center space-x-6 mb-4">
            <div className="w-56">
              <label className="block text-sm font-medium mb-2">Pre-event video duration (sec):</label>
              <div className="relative">
                <input 
                  type="text" 
                  value={getValue('impact_videoBefore')} 
                  onChange={(e) => handleNumberChange('impact_videoBefore', e.target.value)}
                  className="block w-24 py-2 px-3 text-gray-700 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
                <div className="absolute inset-y-0 right-0 flex flex-col h-full">
                  <button 
                    className="h-1/2 px-2 bg-gray-200 hover:bg-gray-300 rounded-tr-md border-l border-gray-300"
                    onClick={() => handleNumberChange('impact_videoBefore', (getValue('impact_videoBefore') + 1).toString())}
                  >
                    ▲
                  </button>
                  <button 
                    className="h-1/2 px-2 bg-gray-200 hover:bg-gray-300 rounded-br-md border-l border-t border-gray-300"
                    onClick={() => handleNumberChange('impact_videoBefore', Math.max(0, getValue('impact_videoBefore') - 1).toString())}
                  >
                    ▼
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-6">
            <div className="w-56">
              <label className="block text-sm font-medium mb-2">Post-event video duration (sec):</label>
              <div className="relative">
                <input 
                  type="text" 
                  value={getValue('impact_videoAfter')} 
                  onChange={(e) => handleNumberChange('impact_videoAfter', e.target.value)}
                  className="block w-24 py-2 px-3 text-gray-700 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
                <div className="absolute inset-y-0 right-0 flex flex-col h-full">
                  <button 
                    className="h-1/2 px-2 bg-gray-200 hover:bg-gray-300 rounded-tr-md border-l border-gray-300"
                    onClick={() => handleNumberChange('impact_videoAfter', (getValue('impact_videoAfter') + 1).toString())}
                  >
                    ▲
                  </button>
                  <button 
                    className="h-1/2 px-2 bg-gray-200 hover:bg-gray-300 rounded-br-md border-l border-t border-gray-300"
                    onClick={() => handleNumberChange('impact_videoAfter', Math.max(0, getValue('impact_videoAfter') - 1).toString())}
                  >
                    ▼
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
  
        <div className="mb-6">
          <h3 className="text-lg font-medium mb-2 text-gray-900">Auto Upload to Portal:</h3>
          <p className="text-sm text-gray-500 mb-6">Choose what kind of data to auto upload to Events for reviewing if the event is triggered.</p>
          
          <div className="mb-4">
            <label className="block text-sm font-medium mb-2">Media:</label>
            <span className="text-gray-900 font-medium">Video</span>
          </div>
          
          <div className="mb-4">
            <div className="flex items-center">
              <label className="block text-sm font-medium mb-2 mr-2">G-sensor & GPS Data:</label>
              <Info size={16} className="text-gray-500" />
            </div>
            <div className="inline-block px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
              On
            </div>
          </div>
        </div>
  
        <div className="mb-6">
          <div className="flex items-center space-x-2 mb-2">
            <h3 className="text-lg font-medium text-gray-900">Email Notification:</h3>
            <Info size={16} className="text-gray-500" />
          </div>
          
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              className="sr-only peer"
              checked={getValue('impact_emailNotify')}
              onChange={() => handleToggle('impact_emailNotify')}
            />
            <div className="w-11 h-6 bg-gray-300 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
          </label>
        </div>
  
        <div className="mb-6">
          <h3 className="text-lg font-medium mb-2 text-gray-900">In-cabin Audio Alert:</h3>
          
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              className="sr-only peer"
              checked={getValue('impact_audioAlert')}
              onChange={() => handleToggle('impact_audioAlert')}
            />
            <div className="w-11 h-6 bg-gray-300 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
          </label>
        </div>
      </div>
    );
  }

  function renderHarshDrivingSettings() {
    return (
     <div className="p-6 bg-white text-gray-800">
      <div className="mb-8">
        <h3 className="text-lg font-medium mb-2 text-gray-900">Event Trigger:</h3>
        <p className="text-sm text-gray-500">Set up the condition of triggering the event</p>
        
        <div className="mt-4 p-4 bg-gray-100 rounded-lg">
          <div className="grid grid-cols-8 gap-4">
            <div className="col-span-2">
              <h4 className="text-sm font-medium mb-4 text-gray-900">Event Type</h4>
            </div>
            <div className="col-span-3">
              <h4 className="text-sm font-medium mb-4 text-gray-900">Trigger Threshold</h4>
            </div>
            <div className="col-span-3">
              <div className="flex justify-between mb-1">
                <div className="text-right">
                  <span className="text-sm font-medium text-gray-900">Sensitivity</span>
                </div>
              </div>
              <div className="flex justify-between mb-1">
                <div>
                  <span className="text-xs text-gray-500">Low<br />Less events</span>
                </div>
                <div className="text-right">
                  <span className="text-xs text-gray-500">High<br />More events</span>
                </div>
              </div>
            </div>
          </div>
          
          {/* Harsh Acceleration */}
          <div className="grid grid-cols-8 gap-4 mb-4">
            <div className="col-span-2">
              <span className="block text-gray-800">Harsh Acceleration</span>
            </div>
            <div className="col-span-3">
              <div className="mb-2">
                <span className="block text-sm text-gray-500 mb-1">Speed:</span>
                <div className="relative">
                  <input 
                    type="text" 
                    value={getValue('harsh_haSpeed')} 
                    onChange={(e) => handleNumberChange('harsh_haSpeed', e.target.value)}
                    className="block w-24 py-1 px-3 text-gray-700 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                  <div className="absolute inset-y-0 right-0 flex flex-col h-full">
                    <button 
                      className="h-1/2 px-2 bg-gray-200 hover:bg-gray-300 rounded-tr-md border-l border-gray-300"
                      onClick={() => handleNumberChange('harsh_haSpeed', (getValue('harsh_haSpeed') + 1).toString())}
                    >
                      ▲
                    </button>
                    <button 
                      className="h-1/2 px-2 bg-gray-200 hover:bg-gray-300 rounded-br-md border-l border-t border-gray-300"
                      onClick={() => handleNumberChange('harsh_haSpeed', Math.max(0, getValue('harsh_haSpeed') - 1).toString())}
                    >
                      ▼
                    </button>
                  </div>
                  <span className="absolute right-12 top-1/2 transform -translate-y-1/2 text-gray-500">Kph</span>
                </div>
              </div>
            </div>
            <div className="col-span-3">
              <input 
                type="range" 
                min="0" 
                max="2" 
                step="1"
                value={getValue('harsh_haSensitive')}
                onChange={(e) => handleSliderChange('harsh_haSensitive', parseInt(e.target.value, 10))}
                className="w-full h-2 bg-gray-300 rounded-lg appearance-none cursor-pointer"
              />
            </div>
          </div>
          
          {/* Harsh Braking */}
          <div className="grid grid-cols-8 gap-4 mb-4">
            <div className="col-span-2">
              <span className="block text-gray-800">Harsh Braking</span>
            </div>
            <div className="col-span-3">
              <div className="mb-2">
                <span className="block text-sm text-gray-500 mb-1">Speed discrepancy:</span>
                <div className="relative">
                  <input 
                    type="text" 
                    value={getValue('harsh_hbSpeed')} 
                    onChange={(e) => handleNumberChange('harsh_hbSpeed', e.target.value)}
                    className="block w-24 py-1 px-3 text-gray-700 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                  <div className="absolute inset-y-0 right-0 flex flex-col h-full">
                    <button 
                      className="h-1/2 px-2 bg-gray-200 hover:bg-gray-300 rounded-tr-md border-l border-gray-300"
                      onClick={() => handleNumberChange('harsh_hbSpeed', (getValue('harsh_hbSpeed') + 1).toString())}
                    >
                      ▲
                    </button>
                    <button 
                      className="h-1/2 px-2 bg-gray-200 hover:bg-gray-300 rounded-br-md border-l border-t border-gray-300"
                      onClick={() => handleNumberChange('harsh_hbSpeed', Math.max(0, getValue('harsh_hbSpeed') - 1).toString())}
                    >
                      ▼
                    </button>
                  </div>
                  <span className="absolute right-12 top-1/2 transform -translate-y-1/2 text-gray-500">Kph</span>
                </div>
              </div>
            </div>
            <div className="col-span-3">
              <input 
                type="range" 
                min="0" 
                max="2" 
                step="1"
                value={getValue('harsh_hbSensitive')}
                onChange={(e) => handleSliderChange('harsh_hbSensitive', parseInt(e.target.value, 10))}
                className="w-full h-2 bg-gray-300 rounded-lg appearance-none cursor-pointer"
              />
            </div>
          </div>
          
          {/* Harsh Cornering */}
          <div className="grid grid-cols-8 gap-4">
            <div className="col-span-2">
              <span className="block text-gray-800">Harsh Cornering</span>
            </div>
            <div className="col-span-3">
              <div className="mb-2">
                <span className="block text-sm text-gray-500 mb-1">Speed:</span>
                <div className="relative">
                  <input 
                    type="text" 
                    value={getValue('harsh_hcSpeed')} 
                    onChange={(e) => handleNumberChange('harsh_hcSpeed', e.target.value)}
                    className="block w-24 py-1 px-3 text-gray-700 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                  <div className="absolute inset-y-0 right-0 flex flex-col h-full">
                    <button 
                      className="h-1/2 px-2 bg-gray-200 hover:bg-gray-300 rounded-tr-md border-l border-gray-300"
                      onClick={() => handleNumberChange('harsh_hcSpeed', (getValue('harsh_hcSpeed') + 1).toString())}
                    >
                      ▲
                    </button>
                    <button 
                      className="h-1/2 px-2 bg-gray-200 hover:bg-gray-300 rounded-br-md border-l border-t border-gray-300"
                      onClick={() => handleNumberChange('harsh_hcSpeed', Math.max(0, getValue('harsh_hcSpeed') - 1).toString())}
                    >
                      ▼
                    </button>
                  </div>
                  <span className="absolute right-12 top-1/2 transform -translate-y-1/2 text-gray-500">Kph</span>
                </div>
              </div>
            </div>
            <div className="col-span-3">
              <input 
                type="range" 
                min="0" 
                max="2" 
                step="1"
                value={getValue('harsh_hcSensitive')}
                onChange={(e) => handleSliderChange('harsh_hcSensitive', parseInt(e.target.value, 10))}
                className="w-full h-2 bg-gray-300 rounded-lg appearance-none cursor-pointer"
              />
            </div>
          </div>
        </div>
      </div>

      <div className="mb-8">
        <h3 className="text-lg font-medium mb-2 text-gray-900">Event Recording:</h3>
        <p className="text-sm text-gray-500 mb-6">Set up the duration of the recorded video when the event is triggered, which will be captured by the device for reviewing.</p>
        
        <div className="flex items-center space-x-6 mb-4">
          <div className="w-56">
            <label className="block text-sm font-medium mb-2 text-gray-800">Pre-event video duration (sec):</label>
            <div className="relative">
              <input 
                type="text" 
                value={getValue('harsh_videoBefore')} 
                onChange={(e) => handleNumberChange('harsh_videoBefore', e.target.value)}
                className="block w-24 py-2 px-3 text-gray-700 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
              <div className="absolute inset-y-0 right-0 flex flex-col h-full">
                <button 
                  className="h-1/2 px-2 bg-gray-200 hover:bg-gray-300 rounded-tr-md border-l border-gray-300"
                  onClick={() => handleNumberChange('harsh_videoBefore', (getValue('harsh_videoBefore') + 1).toString())}
                >
                  ▲
                </button>
                <button 
                  className="h-1/2 px-2 bg-gray-200 hover:bg-gray-300 rounded-br-md border-l border-t border-gray-300"
                  onClick={() => handleNumberChange('harsh_videoBefore', Math.max(0, getValue('harsh_videoBefore') - 1).toString())}
                >
                  ▼
                </button>
              </div>
            </div>
          </div>
        </div>
        
        <div className="flex items-center space-x-6">
          <div className="w-56">
            <label className="block text-sm font-medium mb-2 text-gray-800">Post-event video duration (sec):</label>
            <div className="relative">
              <input 
                type="text" 
                value={getValue('harsh_videoAfter')} 
                onChange={(e) => handleNumberChange('harsh_videoAfter', e.target.value)}
                className="block w-24 py-2 px-3 text-gray-700 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
              <div className="absolute inset-y-0 right-0 flex flex-col h-full">
                <button 
                  className="h-1/2 px-2 bg-gray-200 hover:bg-gray-300 rounded-tr-md border-l border-gray-300"
                  onClick={() => handleNumberChange('harsh_videoAfter', (getValue('harsh_videoAfter') + 1).toString())}
                >
                  ▲
                </button>
                <button 
                  className="h-1/2 px-2 bg-gray-200 hover:bg-gray-300 rounded-br-md border-l border-t border-gray-300"
                  onClick={() => handleNumberChange('harsh_videoAfter', Math.max(0, getValue('harsh_videoAfter') - 1).toString())}
                >
                  ▼
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="mb-8">
        <h3 className="text-lg font-medium mb-2 text-gray-900">Auto Upload to Portal:</h3>
        <p className="text-sm text-gray-500 mb-6">Choose what kind of data to auto upload to Events for reviewing if the event is triggered.</p>
        
        <div className="bg-gray-100 p-4 rounded-lg">
          <div className="grid grid-cols-3 gap-4">
            <div className="flex items-center">
              <span className="block text-sm font-medium text-gray-800">Severity Levels</span>
              <Info size={16} className="text-gray-500 ml-1" />
            </div>
            <div>
              <span className="block text-sm font-medium text-gray-800">Media</span>
            </div>
            <div>
              <span className="block text-sm font-medium text-gray-800">G-sensor & GPS Data</span>
            </div>
          </div>
          
          <div className="grid grid-cols-3 gap-4 py-2 border-t border-gray-300 mt-2">
            <div>
              <span className="block text-gray-800">Minor</span>
            </div>
            <div>
              <span className="block text-gray-800">Video</span>
            </div>
            <div>
              <div className="inline-block px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                On
              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-3 gap-4 py-2 border-t border-gray-300">
            <div>
              <span className="block text-gray-800">Moderate</span>
            </div>
            <div>
              <span className="block text-gray-800">Video</span>
            </div>
            <div>
              <div className="inline-block px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                On
              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-3 gap-4 py-2 border-t border-gray-300">
            <div>
              <span className="block text-gray-800">Severe</span>
            </div>
            <div>
              <span className="block text-gray-800">Video</span>
            </div>
            <div>
              <div className="inline-block px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                On
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="mb-6">
        <div className="flex items-center space-x-2 mb-2">
          <h3 className="text-lg font-medium text-gray-900">Email Notification:</h3>
          <Info size={16} className="text-gray-500" />
        </div>
        
        <select
          value={getValue('harsh_emailNotify') ? 'all' : 'off'}
          onChange={(e) => onChange('harsh_emailNotify', e.target.value !== 'off')}
          className="block w-36 py-2 px-3 text-gray-700 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="off">Off</option>
          <option value="all">All Levels</option>
          <option value="severe">Severe Only</option>
        </select>
      </div>

      <div className="mb-6">
        <h3 className="text-lg font-medium mb-2 text-gray-900">In-cabin Audio Alert:</h3>
        
        <label className="relative inline-flex items-center cursor-pointer">
          <input
            type="checkbox"
            className="sr-only peer"
            checked={getValue('harsh_audioAlert')}
            onChange={() => handleToggle('harsh_audioAlert')}
          />
          <div className="w-11 h-6 bg-gray-300 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
        </label>
      </div>

      <div className="mt-8">
        <button 
          onClick={() => {
            // Reset settings to default values
            const defaultSettings = {
              impactEnable: true,
              harshEnable: true,
              impact_drivingSensitive: 1,
              impact_parkingSensitive: 1,
              impact_videoBefore: 15,
              impact_videoAfter: 16,
              impact_media: 'video',
              impact_gsensor: true,
              impact_emailNotify: true,
              impact_audioAlert: true,
              harsh_haEnable: true,
              harsh_hbEnable: true,
              harsh_hcEnable: true,
              harsh_haSpeed: 1,
              harsh_hbSpeed: 11,
              harsh_hcSpeed: 30,
              harsh_haSensitive: 1,
              harsh_hbSensitive: 1,
              harsh_hcSensitive: 1,
              harsh_videoBefore: 10,
              harsh_videoAfter: 3,
              harsh_minorMedia: 'video',
              harsh_emailNotify: false,
              harsh_audioAlert: true
            };

            Object.entries(defaultSettings).forEach(([key, value]) => {
              onChange(key, value);
            });
          }}
          className="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-full"
        >
          Restore to Default
        </button>
      </div>
    </div>
);
}
};

// Now let's create the white background version of the same component
const SensorSettingsWhiteBg: React.FC<SensorSettingsProps> = ({
settings,
onChange,
modifiedValues,
}) => {
const [selectedFeature, setSelectedFeature] = useState('impact');

// Helper function to get the current value (from modified values or original settings)
const getValue = (key: string) => {
return modifiedValues[key] !== undefined ? modifiedValues[key] : settings[key];
};

// Handle toggle changes
const handleToggle = (key: string) => {
onChange(key, !getValue(key));
};

// Handle slider changes
const handleSliderChange = (key: string, value: number) => {
onChange(key, value);
};

// Handle input number changes
const handleNumberChange = (key: string, value: string) => {
const numValue = parseInt(value, 10);
if (!isNaN(numValue)) {
onChange(key, numValue);
}
};

// Handle select changes
const handleSelectChange = (key: string, value: string) => {
onChange(key, value);
};

return (
<div className="grid grid-cols-12 gap-6">
{/* Left side - toggles */}
<div className="col-span-3 bg-gray-800 text-white rounded-lg">
<div 
className={`flex justify-between items-center p-4 cursor-pointer ${selectedFeature === 'impact' ? 'bg-blue-800' : ''}`}
onClick={() => setSelectedFeature('impact')}
>
<span className="font-medium">Impact</span>
<label className="relative inline-flex items-center cursor-pointer">
<input
type="checkbox"
className="sr-only peer"
checked={getValue('impactEnable')}
onChange={() => handleToggle('impactEnable')}
/>
<div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
</label>
</div>

<div 
className={`flex justify-between items-center p-4 cursor-pointer border-t border-gray-700 ${selectedFeature === 'harshDriving' ? 'bg-blue-800' : ''}`}
onClick={() => setSelectedFeature('harshDriving')}
>
<span className="font-medium">Harsh Driving</span>
<label className="relative inline-flex items-center cursor-pointer">
<input
type="checkbox"
className="sr-only peer"
checked={getValue('harshEnable')}
onChange={() => handleToggle('harshEnable')}
/>
<div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
</label>
</div>
</div>

{/* Right side - configuration */}
<div className="col-span-9 bg-white text-gray-800 rounded-lg shadow-md">
{selectedFeature === 'impact' && renderImpactSettings()}
{selectedFeature === 'harshDriving' && renderHarshDrivingSettings()}
</div>
</div>
);

function renderImpactSettings() {
return (
<div className="p-6">
<div className="mb-8">
<h3 className="text-lg font-medium mb-2">Event Trigger:</h3>
<p className="text-sm text-gray-600">Set up the condition of triggering the event</p>

<div className="mt-4 p-4 bg-gray-100 rounded-lg">
<div className="mb-6">
<h4 className="text-sm font-medium mb-2">Event Type</h4>

<div className="grid grid-cols-3 gap-4 mb-4">
<div className="col-span-1">
  <span className="block mb-2">Driving Impact</span>
</div>
<div className="col-span-2">
  <div className="flex justify-between mb-1">
    <div className="text-right">
      <span className="text-sm font-medium">Sensitivity</span>
    </div>
  </div>
  <div className="flex justify-between mb-1">
    <div>
      <span className="text-xs text-gray-500">Low<br />Less events</span>
    </div>
    <div className="text-right">
      <span className="text-xs text-gray-500">High<br />More events</span>
    </div>
  </div>
  
  <input 
    type="range" 
    min="0" 
    max="2" 
    step="1"
    value={getValue('impact_drivingSensitive')}
    onChange={(e) => handleSliderChange('impact_drivingSensitive', parseInt(e.target.value, 10))}
    className="w-full h-2 bg-gray-300 rounded-lg appearance-none cursor-pointer"
  />
</div>
</div>

<div className="grid grid-cols-3 gap-4">
<div className="col-span-1">
  <span className="block mb-2">Parking Impact</span>
</div>
<div className="col-span-2">
  <input 
    type="range" 
    min="0" 
    max="2" 
    step="1"
    value={getValue('impact_parkingSensitive')}
    onChange={(e) => handleSliderChange('impact_parkingSensitive', parseInt(e.target.value, 10))}
    className="w-full h-2 bg-gray-300 rounded-lg appearance-none cursor-pointer"
  />
</div>
</div>
</div>
</div>
</div>

<div className="mb-8">
<h3 className="text-lg font-medium mb-2">Event Recording:</h3>
<p className="text-sm text-gray-600 mb-6">Set up the duration of the recorded video when the event is triggered, which will be captured by the device for reviewing.</p>

<div className="flex items-center space-x-6 mb-4">
<div className="w-56">
<label className="block text-sm font-medium mb-2">Pre-event video duration (sec):</label>
<div className="relative">
<input 
  type="text" 
  value={getValue('impact_videoBefore')} 
  onChange={(e) => handleNumberChange('impact_videoBefore', e.target.value)}
  className="block w-24 py-2 px-3 text-gray-700 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
/>
<div className="absolute inset-y-0 right-0 flex flex-col h-full">
  <button 
    className="h-1/2 px-2 bg-gray-200 hover:bg-gray-300 rounded-tr-md border-l border-gray-300"
    onClick={() => handleNumberChange('impact_videoBefore', (getValue('impact_videoBefore') + 1).toString())}
  >
    ▲
  </button>
  <button 
    className="h-1/2 px-2 bg-gray-200 hover:bg-gray-300 rounded-br-md border-l border-t border-gray-300"
    onClick={() => handleNumberChange('impact_videoBefore', Math.max(0, getValue('impact_videoBefore') - 1).toString())}
  >
    ▼
  </button>
</div>
</div>
</div>
</div>

<div className="flex items-center space-x-6">
<div className="w-56">
<label className="block text-sm font-medium mb-2">Post-event video duration (sec):</label>
<div className="relative inline-block">
<input 
  type="text" 
  value={getValue('impact_videoAfter')} 
  onChange={(e) => handleNumberChange('impact_videoAfter', e.target.value)}
  className="block w-24 py-2 px-3 text-gray-700 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
/>
<div className="absolute inset-y-0 right-0 flex flex-col h-full">
  <button 
    className="h-1/2 px-2 bg-gray-200 hover:bg-gray-300 rounded-tr-md border-l border-gray-300"
    onClick={() => handleNumberChange('impact_videoAfter', (getValue('impact_videoAfter') + 1).toString())}
  >
    ▲
  </button>
  <button 
    className="h-1/2 px-2 bg-gray-200 hover:bg-gray-300 rounded-br-md border-l border-t border-gray-300"
    onClick={() => handleNumberChange('impact_videoAfter', Math.max(0, getValue('impact_videoAfter') - 1).toString())}
  >
    ▼
  </button>
</div>
</div>
</div>
</div>
</div>

<div className="mb-6">
<h3 className="text-lg font-medium mb-2">Auto Upload to Portal:</h3>
<p className="text-sm text-gray-600 mb-6">Choose what kind of data to auto upload to Events for reviewing if the event is triggered.</p>

<div className="mb-4">
<label className="block text-sm font-medium mb-2">Media:</label>
<span className="text-gray-800 font-medium">Video</span>
</div>

<div className="mb-4">
<div className="flex items-center">
<label className="block text-sm font-medium mb-2 mr-2">G-sensor & GPS Data:</label>
<Info size={16} className="text-gray-500" />
</div>
<div className="inline-block px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
On
</div>
</div>
</div>

<div className="mb-6">
<div className="flex items-center space-x-2 mb-2">
<h3 className="text-lg font-medium">Email Notification:</h3>
<Info size={16} className="text-gray-500" />
</div>

<label className="relative inline-flex items-center cursor-pointer">
<input
type="checkbox"
className="sr-only peer"
checked={getValue('impact_emailNotify')}
onChange={() => handleToggle('impact_emailNotify')}
/>
<div className="w-11 h-6 bg-gray-300 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
</label>
</div>

<div className="mb-6">
<h3 className="text-lg font-medium mb-2">In-cabin Audio Alert:</h3>

<label className="relative inline-flex items-center cursor-pointer">
<input
type="checkbox"
className="sr-only peer"
checked={getValue('impact_audioAlert')}
onChange={() => handleToggle('impact_audioAlert')}
/>
<div className="w-11 h-6 bg-gray-300 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
</label>
</div>
</div>
);
}

function renderHarshDrivingSettings() {
return (
<div className="p-6">
<div className="mb-8">
<h3 className="text-lg font-medium mb-2">Event Trigger:</h3>
<p className="text-sm text-gray-600">Set up the condition of triggering the event</p>

<div className="mt-4 p-4 bg-gray-100 rounded-lg">
<div className="grid grid-cols-8 gap-4">
<div className="col-span-2">
<h4 className="text-sm font-medium mb-4">Event Type</h4>
</div>
<div className="col-span-3">
<h4 className="text-sm font-medium mb-4">Trigger Threshold</h4>
</div>
<div className="col-span-3">
<div className="flex justify-between mb-1">
  <div className="text-right">
    <span className="text-sm font-medium">Sensitivity</span>
  </div>
</div>
<div className="flex justify-between mb-1">
  <div>
    <span className="text-xs text-gray-500">Low<br />Less events</span>
  </div>
  <div className="text-right">
    <span className="text-xs text-gray-500">High<br />More events</span>
  </div>
</div>
</div>
</div>

{/* Harsh Acceleration */}
<div className="grid grid-cols-8 gap-4 mb-4">
<div className="col-span-2">
<span className="block">Harsh Acceleration</span>
</div>
<div className="col-span-3">
<div className="mb-2">
  <span className="block text-sm text-gray-600 mb-1">Speed:</span>
  <div className="relative">
    <input 
      type="text" 
      value={getValue('harsh_haSpeed')} 
      onChange={(e) => handleNumberChange('harsh_haSpeed', e.target.value)}
      className="block w-24 py-1 px-3 text-gray-700 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
    />
    <div className="absolute inset-y-0 right-0 flex flex-col h-full">
      <button 
        className="h-1/2 px-2 bg-gray-200 hover:bg-gray-300 rounded-tr-md border-l border-gray-300"
        onClick={() => handleNumberChange('harsh_haSpeed', (getValue('harsh_haSpeed') + 1).toString())}
      >
        ▲
      </button>
      <button 
        className="h-1/2 px-2 bg-gray-200 hover:bg-gray-300 rounded-br-md border-l border-t border-gray-300"
        onClick={() => handleNumberChange('harsh_haSpeed', Math.max(0, getValue('harsh_haSpeed') - 1).toString())}
      >
        ▼
      </button>
    </div>
    <span className="absolute right-12 top-1/2 transform -translate-y-1/2 text-gray-600">Kph</span>
  </div>
</div>
</div>
<div className="col-span-3">
<input 
  type="range" 
  min="0" 
  max="2" 
  step="1"
  value={getValue('harsh_haSensitive')}
  onChange={(e) => handleSliderChange('harsh_haSensitive', parseInt(e.target.value, 10))}
  className="w-full h-2 bg-gray-300 rounded-lg appearance-none cursor-pointer"
/>
</div>
</div>

{/* Harsh Braking */}
<div className="grid grid-cols-8 gap-4 mb-4">
<div className="col-span-2">
<span className="block">Harsh Braking</span>
</div>
<div className="col-span-3">
<div className="mb-2">
  <span className="block text-sm text-gray-600 mb-1">Speed discrepancy:</span>
  <div className="relative">
    <input 
      type="text" 
      value={getValue('harsh_hbSpeed')} 
      onChange={(e) => handleNumberChange('harsh_hbSpeed', e.target.value)}
      className="block w-24 py-1 px-3 text-gray-700 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
    />
    <div className="absolute inset-y-0 right-0 flex flex-col h-full">
      <button 
        className="h-1/2 px-2 bg-gray-200 hover:bg-gray-300 rounded-tr-md border-l border-gray-300"
        onClick={() => handleNumberChange('harsh_hbSpeed', (getValue('harsh_hbSpeed') + 1).toString())}
      >
        ▲
      </button>
      <button 
        className="h-1/2 px-2 bg-gray-200 hover:bg-gray-300 rounded-br-md border-l border-t border-gray-300"
        onClick={() => handleNumberChange('harsh_hbSpeed', Math.max(0, getValue('harsh_hbSpeed') - 1).toString())}
      >
        ▼
      </button>
    </div>
    <span className="absolute right-12 top-1/2 transform -translate-y-1/2 text-gray-600">Kph</span>
  </div>
</div>
</div>
<div className="col-span-3">
<input 
  type="range" 
  min="0" 
  max="2" 
  step="1"
  value={getValue('harsh_hbSensitive')}
  onChange={(e) => handleSliderChange('harsh_hbSensitive', parseInt(e.target.value, 10))}
  className="w-full h-2 bg-gray-300 rounded-lg appearance-none cursor-pointer"
/>
</div>
</div>

{/* Harsh Cornering */}
<div className="grid grid-cols-8 gap-4">
<div className="col-span-2">
<span className="block">Harsh Cornering</span>
</div>
<div className="col-span-3">
<div className="mb-2">
  <span className="block text-sm text-gray-600 mb-1">Speed:</span>
  <div className="relative">
    <input 
      type="text" 
      value={getValue('harsh_hcSpeed')} 
      onChange={(e) => handleNumberChange('harsh_hcSpeed', e.target.value)}
      className="block w-24 py-1 px-3 text-gray-700 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
    />
    <div className="absolute inset-y-0 right-0 flex flex-col h-full">
      <button 
        className="h-1/2 px-2 bg-gray-200 hover:bg-gray-300 rounded-tr-md border-l border-gray-300"
        onClick={() => handleNumberChange('harsh_hcSpeed', (getValue('harsh_hcSpeed') + 1).toString())}
      >
        ▲
      </button>
      <button 
        className="h-1/2 px-2 bg-gray-200 hover:bg-gray-300 rounded-br-md border-l border-t border-gray-300"
        onClick={() => handleNumberChange('harsh_hcSpeed', Math.max(0, getValue('harsh_hcSpeed') - 1).toString())}
      >
        ▼
      </button>
    </div>
    <span className="absolute right-12 top-1/2 transform -translate-y-1/2 text-gray-600">Kph</span>
  </div>
</div>
</div>
<div className="col-span-3">
<input 
  type="range" 
  min="0" 
  max="2" 
  step="1"
  value={getValue('harsh_hcSensitive')}
  onChange={(e) => handleSliderChange('harsh_hcSensitive', parseInt(e.target.value, 10))}
  className="w-full h-2 bg-gray-300 rounded-lg appearance-none cursor-pointer"
/>
</div>
</div>
</div>
</div>

<div className="mb-8">
<h3 className="text-lg font-medium mb-2">Event Recording:</h3>
<p className="text-sm text-gray-600 mb-6">Set up the duration of the recorded video when the event is triggered, which will be captured by the device for reviewing.</p>

<div className="flex items-center space-x-6 mb-4">
<div className="w-56">
<label className="block text-sm font-medium mb-2">Pre-event video duration (sec):</label>
<div className="relative">
<input 
  type="text" 
  value={getValue('harsh_videoBefore')} 
  onChange={(e) => handleNumberChange('harsh_videoBefore', e.target.value)}
  className="block w-24 py-2 px-3 text-gray-700 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
/>
<div className="absolute inset-y-0 right-0 flex flex-col h-full">
  <button 
    className="h-1/2 px-2 bg-gray-200 hover:bg-gray-300 rounded-tr-md border-l border-gray-300"
    onClick={() => handleNumberChange('harsh_videoBefore', (getValue('harsh_videoBefore') + 1).toString())}
  >
    ▲
  </button>
  <button 
    className="h-1/2 px-2 bg-gray-200 hover:bg-gray-300 rounded-br-md border-l border-t border-gray-300"
    onClick={() => handleNumberChange('harsh_videoBefore', Math.max(0, getValue('harsh_videoBefore') - 1).toString())}
  >
    ▼
  </button>
</div>
</div>
</div>
</div>

<div className="flex items-center space-x-6">
<div className="w-56">
<label className="block text-sm font-medium mb-2">Post-event video duration (sec):</label>
<div className="relative">
<input 
  type="text" 
  value={getValue('harsh_videoAfter')} 
  onChange={(e) => handleNumberChange('harsh_videoAfter', e.target.value)}
  className="block w-24 py-2 px-3 text-gray-700 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
/>
<div className="absolute inset-y-0 right-0 flex flex-col h-full">
  <button 
    className="h-1/2 px-2 bg-gray-200 hover:bg-gray-300 rounded-tr-md border-l border-gray-300"
    onClick={() => handleNumberChange('harsh_videoAfter', (getValue('harsh_videoAfter') + 1).toString())}
  >
    ▲
  </button>
  <button 
    className="h-1/2 px-2 bg-gray-200 hover:bg-gray-300 rounded-br-md border-l border-t border-gray-300"
    onClick={() => handleNumberChange('harsh_videoAfter', Math.max(0, getValue('harsh_videoAfter') - 1).toString())}
  >
    ▼
  </button>
</div>
</div>
</div>
</div>
</div>

<div className="mb-8">
<h3 className="text-lg font-medium mb-2">Auto Upload to Portal:</h3>
<p className="text-sm text-gray-600 mb-6">Choose what kind of data to auto upload to Events for reviewing if the event is triggered.</p>

<div className="bg-gray-100 p-4 rounded-lg">
<div className="grid grid-cols-3 gap-4">
<div className="flex items-center">
<span className="block text-sm font-medium">Severity Levels</span>
<Info size={16} className="text-gray-500 ml-1" />
</div>
<div>
<span className="block text-sm font-medium">Media</span>
</div>
<div>
<span className="block text-sm font-medium">G-sensor & GPS Data</span>
</div>
</div>

<div className="grid grid-cols-3 gap-4 py-2 border-t border-gray-300 mt-2">
<div>
<span className="block">Minor</span>
</div>
<div>
<span className="block">Video</span>
</div>
<div>
<div className="inline-block px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
  On
</div>
</div>
</div>

<div className="grid grid-cols-3 gap-4 py-2 border-t border-gray-300">
<div>
<span className="block">Moderate</span>
</div>
<div>
<span className="block">Video</span>
</div>
<div>
<div className="inline-block px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
  On
</div>
</div>
</div>

<div className="grid grid-cols-3 gap-4 py-2 border-t border-gray-300">
<div>
<span className="block">Severe</span>
</div>
<div>
<span className="block">Video</span>
</div>
<div>
<div className="inline-block px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
  On
</div>
</div>
</div>
</div>
</div>

<div className="mb-6">
<div className="flex items-center space-x-2 mb-2">
<h3 className="text-lg font-medium">Email Notification:</h3>
<Info size={16} className="text-gray-500" />
</div>

<select
value={getValue('harsh_emailNotify') ? 'all' : 'off'}
onChange={(e) => onChange('harsh_emailNotify', e.target.value !== 'off')}
className="block w-36 py-2 px-3 text-gray-700 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
>
<option value="off">Off</option>
<option value="all">All Levels</option>
<option value="severe">Severe Only</option>
</select>
</div>

<div className="mb-6">
<h3 className="text-lg font-medium mb-2">In-cabin Audio Alert:</h3>

<label className="relative inline-flex items-center cursor-pointer">
<input
type="checkbox"
className="sr-only peer"
checked={getValue('harsh_audioAlert')}
onChange={() => handleToggle('harsh_audioAlert')}
/>
// src/components/configuration/SensorSettings.tsx (continued from previous response)
            <div className="w-11 h-6 bg-gray-300 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
          </label>
        </div>

        <div className="mt-8">
          <button 
            onClick={() => {
              // Reset settings to default values
              const defaultSettings = {
                impactEnable: true,
                harshEnable: true,
                impact_drivingSensitive: 1,
                impact_parkingSensitive: 1,
                impact_videoBefore: 15,
                impact_videoAfter: 16,
                impact_media: 'video',
                impact_gsensor: true,
                impact_emailNotify: true,
                impact_audioAlert: true,
                harsh_haEnable: true,
                harsh_hbEnable: true,
                harsh_hcEnable: true,
                harsh_haSpeed: 1,
                harsh_hbSpeed: 11,
                harsh_hcSpeed: 30,
                harsh_haSensitive: 1,
                harsh_hbSensitive: 1,
                harsh_hcSensitive: 1,
                harsh_videoBefore: 10,
                harsh_videoAfter: 3,
                harsh_minorMedia: 'video',
                harsh_emailNotify: false,
                harsh_audioAlert: true
              };
              
              Object.entries(defaultSettings).forEach(([key, value]) => {
                onChange(key, value);
              });
            }}
            className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-full"
          >
            Restore to Default
          </button>
        </div>
      </div>
    );
  }
};

// Export the white background version
export default SensorSettings;