// src/components/common/LoadingSpinner.tsx
import React from 'react';
import { Spinner } from 'react-bootstrap';
import './LoadingSpinner.css';

interface LoadingSpinnerProps {
  text?: string;
  fullPage?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  text = 'Loading...', 
  fullPage = true,
  size = 'md'
}) => {
  const getSpinnerSize = () => {
    switch (size) {
      case 'sm': return '';  // default Bootstrap size
      case 'md': return '2rem';
      case 'lg': return '3rem';
      default: return '2rem';
    }
  };

  const spinnerSize = getSpinnerSize();

  return (
    <div className={`loading-spinner ${fullPage ? 'full-page' : ''}`}>
      <div className="spinner-container">
        <Spinner 
          animation="border" 
          role="status"
          style={{ 
            width: spinnerSize, 
            height: spinnerSize 
          }}
        >
          <span className="visually-hidden">Loading...</span>
        </Spinner>
        {text && <p className="mt-3">{text}</p>}
      </div>
    </div>
  );
};

export default LoadingSpinner;