import { useState, useEffect, useRef } from 'react';

const VisionMaxLiveView = ({ 
  device, 
  onError,
  onClose,
  modalStyle = true,
  title = "Live View" 
}) => {
  const videoRef = useRef(null);
  const [isConnecting, setIsConnecting] = useState(true);
  const [connectionError, setConnectionError] = useState(null);
  const [connectionProgress, setConnectionProgress] = useState(0);
  const [isMuted, setIsMuted] = useState(true);
  const [remainingTime, setRemainingTime] = useState(null);
  
  // Reference to vmRTC instance
  const vmRTCRef = useRef(null);
  //const token = typeof window !== 'undefined' ? localStorage.getItem('authToken') : null;
  // Configuration object
  const config = {
    env: 'prod',
    apiKey: 'WnbpXF1lJ1vKePo1C5pCXbgVGbkwehE8wMvX5LV9ikQ',
    clientAccessToken: localStorage.getItem('authToken'),
    feature: { 
      enableDefaultCameraSwitchButton: true 
    }
  };

  // Initialize and connect to VisionMax
  useEffect(() => {
    if (!window.vmRTC) {
      setConnectionError("VisionMax RTC library not available");
      if (onError) onError(new Error("vmRTC not loaded"));
      return;
    }

    // Check for required props
    if (!device || !device.cid) {
      setConnectionError("Missing device information");
      if (onError) onError(new Error("Missing device CID"));
      return;
   }

   const initTimeout = setTimeout(() => {
    initConnection();
  }, 500);

   
    
    // Cleanup on unmount
    return () => {
      clearTimeout(initTimeout);
      cleanupConnection();
    };
  }, [device, onError]);

  const initConnection = () => {
    // Initialize vmRTC
    setIsConnecting(true);
    const rtc = window.vmRTC;
    vmRTCRef.current = rtc;
    
    // Set up event listeners
    rtc.addEventListener("progressStatusUpdated", (percentage) => {
      console.log('Progress updated:', percentage);
      setConnectionProgress(parseInt(percentage, 10));
    });
    
    rtc.addEventListener("connect fail", (errorMsg) => {
      console.error('Connection failed:', errorMsg);
      setIsConnecting(false);
      setConnectionError("Failed to connect to the device. Please try again.");
      if (onError) onError(new Error("Connection failure"));
    });
    
    rtc.addEventListener("initial.fail", (errorMsg) => {
      console.error('Initialization failed:', errorMsg);
      setIsConnecting(false);
      setConnectionError(`Failed to initialize the connection: ${errorMsg}`);
      if (onError) onError(new Error(`Initialization failure: ${errorMsg}`));
    });
    
    rtc.addEventListener("connection lost", () => {
      console.error('Connection lost');
      setIsConnecting(false);
      setConnectionError("Connection to the device was lost.");
      if (onError) onError(new Error("Connection lost"));
    });
    
    rtc.addEventListener("peer connected", () => {
      console.log('Peer connected');
      setIsConnecting(false);
      setConnectionError(null);
      startSessionTimer();
    });
    
    rtc.addEventListener("session stable", () => {
      console.log('Session stable');
    });
    
    // Start the connection
    rtc.setup(config).start(device.cid);
 }
  
  // Timer for the session
  const startSessionTimer = () => {
    let seconds = 600; // 10 minutes
    
    const updateTimer = () => {
      const minutes = Math.floor(seconds / 60);
      const remainingSecs = seconds % 60;
      const timeString = `${minutes}:${remainingSecs.toString().padStart(2, '0')}`;
      setRemainingTime(timeString);
    };
    
    updateTimer();
    
    const timerInterval = setInterval(() => {
      seconds--;
      updateTimer();      
      if (seconds <= 0) {
        clearInterval(timerInterval);
        handleClose();
      }
    }, 1000);
    
    return timerInterval;
  };
  
  // Function to clean up connection
  const cleanupConnection = () => {
    if (vmRTCRef.current) {
      vmRTCRef.current.stop();
      vmRTCRef.current = null;
    }
  };
  
  // Handle camera switch
  const handleSwitchCamera = () => {
    if (vmRTCRef.current) {
      vmRTCRef.current.nextCam();
    }
  };
  
  // Handle taking a snapshot of the video
  const handleSnapshot = () => {
    if (videoRef.current && videoRef.current.readyState >= videoRef.current.HAVE_CURRENT_DATA) {
      const canvas = document.createElement('canvas');
      canvas.width = videoRef.current.videoWidth;
      canvas.height = videoRef.current.videoHeight;
      
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.drawImage(videoRef.current, 0, 0, canvas.width, canvas.height);
        const dataUrl = canvas.toDataURL('image/png');
        
        const link = document.createElement('a');
        link.href = dataUrl;
        const deviceId = device?.id || device?.serialNumber || 'unknown';
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        link.download = `liveview_${deviceId}_${timestamp}.png`;
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    }
  };
  
  // Handle closing the view
  const handleClose = () => {
    cleanupConnection();
    if (onClose) onClose();
  };
  
  // Handle retry connection
  const handleRetry = () => {
    setIsConnecting(true);
    setConnectionError(null);
    cleanupConnection();
    
    // Short delay before retry
    setTimeout(() => {
      const rtc = window.vmRTC;
      vmRTCRef.current = rtc;
      rtc.setup(config).start(device.cid);
    }, 1000);
  };
  
  // Toggle audio mute
  const toggleMute = () => {
    if (videoRef.current && videoRef.current.srcObject) {
      const stream = videoRef.current.srcObject;
      const audioTracks = stream.getAudioTracks();
      
      if (audioTracks.length > 0) {
        const newMutedState = !isMuted;
        audioTracks.forEach(track => {
          track.enabled = !newMutedState;
        });
        setIsMuted(newMutedState);
      }
    }
  };

  // Modal-style UI
  if (modalStyle) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75">
        <div className="bg-white rounded-lg shadow-xl overflow-hidden w-full max-w-4xl mx-4 flex flex-col" style={{ maxHeight: '90vh' }}>
          {/* Header */}
          <div className="flex items-center justify-between p-4 bg-gray-100 border-b border-gray-300">
            <div>
              <h3 className="text-lg font-medium text-gray-900">{title}</h3>
              <p className="text-sm text-gray-600">
                Device: {device?.id || device?.serialNumber || 'Unknown'}
                {remainingTime && <span className="ml-4">Time Left: {remainingTime}</span>}
              </p>
            </div>
            <button
              title="Close Live View"
              className="text-gray-500 hover:text-gray-800 focus:outline-none p-1 rounded-full hover:bg-gray-200"
              onClick={handleClose}
            >
              <span className="sr-only">Close</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          {/* Video container */}
          <div className="relative bg-black flex-grow flex items-center justify-center" style={{ minHeight: '300px' }}>
            <video
              ref={videoRef}
              id="rtc_liveview" // IMPORTANT: vmRTC requires this specific ID
              className="w-full h-full object-contain"
              autoPlay
              playsInline
              muted={isMuted}
            />
            
            {/* Loading Overlay */}
            {isConnecting && !connectionError && (
              <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-70 text-white">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-white mx-auto mb-3"></div>
                  <p>Connecting to device camera...</p>
                  <div className="mt-3 w-64 bg-gray-700 rounded-full h-2.5">
                    <div className="bg-blue-600 h-2.5 rounded-full" style={{ width: `${connectionProgress}%` }}></div>
                  </div>
                </div>
              </div>
            )}
            
            {/* Error Overlay */}
            {connectionError && (
              <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-80 text-white p-4">
                <div className="text-center">
                  <svg 
                    xmlns="http://www.w3.org/2000/svg" 
                    width="40" 
                    height="40" 
                    className="text-red-500 mx-auto mb-3"
                    fill="none" 
                    viewBox="0 0 24 24" 
                    stroke="currentColor"
                  >
                    <path 
                      strokeLinecap="round" 
                      strokeLinejoin="round" 
                      strokeWidth={2} 
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" 
                    />
                  </svg>
                  <h3 className="text-xl font-semibold mb-2">Connection Error</h3>
                  <p className="mb-4 text-gray-300">{connectionError}</p>
                  <button
                    className="mt-2 px-5 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                    onClick={handleRetry}
                  >
                    Try Again
                  </button>
                  <button
                    className="mt-2 ml-2 px-5 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 focus:outline-none"
                    onClick={handleClose}
                  >
                    Close
                  </button>
                </div>
              </div>
            )}
            
            {/* Live indicator and controls */}
            {!isConnecting && !connectionError && (
              <>
                <div className="absolute top-4 left-4 flex items-center bg-red-600 text-white px-2 py-1 rounded-sm text-xs shadow">
                  <span className="w-2 h-2 bg-white rounded-full mr-1.5 animate-pulse"></span>
                  LIVE
                </div>
                
                <div className="absolute bottom-4 right-4 flex items-center space-x-2">
                  <button
                    title={isMuted ? "Unmute Audio" : "Mute Audio"}
                    onClick={toggleMute}
                    className="bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70"
                  >
                    {isMuted ? (
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" clipRule="evenodd" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2" />
                      </svg>
                    ) : (
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" />
                      </svg>
                    )}
                  </button>
                  <button
                    title="Take Snapshot"
                    onClick={handleSnapshot}
                    className="bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </button>
                  <button
                    title="Switch Camera"
                    onClick={handleSwitchCamera}
                    className="bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                  </button>
                </div>
              </>
            )}
          </div>
          
          {/* Footer */}
          <div className="px-4 py-3 bg-gray-100 border-t border-gray-300">
            <div className="flex items-center justify-between">
              <p className="text-xs text-gray-500">
                {remainingTime ? `Session ends automatically.` : `Establishing connection...`}
              </p>
              <button
                className="px-4 py-1.5 bg-gray-500 text-white text-sm rounded hover:bg-gray-600 focus:outline-none"
                onClick={handleClose}
              >
                Close View
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }
  
  // Embedded style (non-modal version)
  return (
    <div className="vmx-liveview relative w-full h-full bg-black rounded-lg overflow-hidden">
      <video
        ref={videoRef}
        id="rtc_liveview"
        className="w-full h-full object-contain"
        autoPlay
        playsInline
        muted={isMuted}
      />
      
      {/* Loading Overlay */}
      {isConnecting && !connectionError && (
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 text-white">
          <div className="text-center">
            <div className="animate-spin rounded-full h-6 w-6 border-2 border-white border-t-transparent"></div>
            <p className="mt-2">Connecting...</p>
            <div className="mt-2 w-32 bg-gray-600 rounded-full h-1.5">
              <div className="bg-white h-1.5 rounded-full" style={{ width: `${connectionProgress}%` }}></div>
            </div>
          </div>
        </div>
      )}
      
      {/* Error display */}
      {connectionError && (
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-70 text-white">
          <div className="p-3 rounded text-center">
            <p>{connectionError}</p>
            <div className="mt-2">
              <button className="text-sm bg-blue-500 text-white px-2 py-1 rounded mr-2" onClick={handleRetry}>
                Retry
              </button>
              <button className="text-sm bg-gray-500 text-white px-2 py-1 rounded" onClick={handleClose}>
                Close
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Minimal controls for embedded version */}
      {!isConnecting && !connectionError && (
        <div className="absolute bottom-2 right-2 flex gap-1">
          <button
            onClick={handleSwitchCamera}
            className="bg-black bg-opacity-50 text-white p-1 rounded"
            title="Switch Camera"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>
        </div>
      )}
    </div>
  );
};

export default VisionMaxLiveView;


