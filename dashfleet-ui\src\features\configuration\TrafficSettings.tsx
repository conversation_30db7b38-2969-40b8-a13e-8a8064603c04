import React, { useState } from 'react';
import { Info } from 'lucide-react';

interface SensorSettingsProps {
    settings: Record<string, any>;
    onChange: (key: string, value: any) => void;
    modifiedValues: Record<string, any>;
  }
  
const TrafficSettings = ({
    settings,
    onChange,
    modifiedValues,
  }) => {
    const [activeTab, setActiveTab] = useState('speedCam');
    // const [settings, setSettings] = useState({
    //     // General settings for each tab
    //     speedCam: {
    //       enabled: true,
    //       preEventDuration: 5,
    //       postEventDuration: 0,
    //       media: 'video',
    //       gSensorGpsData: true,
    //       emailNotification: false,
    //       approachingAlert: false,
    //       violateAlert: true,
    //       speedThreshold: 10
    //     },
    //     rollingStop: {
    //       enabled: true,
    //       preEventDuration: 5,
    //       postEventDuration: 3,
    //       media: 'video',
    //       gSensorGpsData: true,
    //       emailNotification: false,
    //       approachingAlert: false,
    //       violateAlert: true
    //     },
    //     railroadCrossing: {
    //       enabled: false,
    //       preEventDuration: 5,
    //       postEventDuration: 0,
    //       media: 'video',
    //       gSensorGpsData: true,
    //       emailNotification: false,
    //       approachingAlert: false,
    //       violateAlert: true
    //     },
    //     schoolZoneSpeed: {
    //       enabled: false,
    //       preEventDuration: 5,
    //       postEventDuration: 0,
    //       media: 'video',
    //       gSensorGpsData: true,
    //       emailNotification: false,
    //       approachingAlert: false,
    //       violateAlert: true
    //     }
    //   });

  // Helper to get settings value
  const getValue = (key: string) => {
    return modifiedValues[key] !== undefined ? modifiedValues[key] : settings[key];
  };

  // Handler for toggle switches
  const handleToggle = (key) => {
    onChange(prev => ({
      ...prev,
      [activeTab]: {
        ...prev[activeTab],
        [key]: !prev[activeTab][key]
      }
    }));
  };

  const handleSidebarToggle = (tabName) => {
    onChange(prev => ({
      ...prev,
      [tabName]: {
        ...prev[tabName],
        enabled: !prev[tabName].enabled
      }
    }));
  };
  // Handler for number input changes
  const handleNumberChange = (key: string, value: string) => {
    const numValue = parseInt(value, 10);
    if (!isNaN(numValue)) {
      onChange(key, numValue);
    }
  };


  // Handler for dropdown changes
  const handleDropdownChange = (key, value) => {
    onChange(key, value);
  };

  const handleRestoreDefault = () => {
    const defaultSettings = {
      speedCam: {
        enabled: true,
        preEventDuration: 5,
        postEventDuration: 0,
        media: 'video',
        gSensorGpsData: true,
        emailNotification: false,
        approachingAlert: false,
        violateAlert: true,
        speedThreshold: 10

      },
      rollingStop: {
        enabled: true,
        preEventDuration: 5,
        postEventDuration: 3,
        media: 'video',
        gSensorGpsData: true,
        emailNotification: false,
        approachingAlert: false,
        violateAlert: true
      },
      railroadCrossing: {
        enabled: false,
        preEventDuration: 5,
        postEventDuration: 0,
        media: 'video',
        gSensorGpsData: true,
        emailNotification: false,
        approachingAlert: false,
        violateAlert: true
      },
      schoolZoneSpeed: {
        enabled: false,
        preEventDuration: 5,
        postEventDuration: 0,
        media: 'video',
        gSensorGpsData: true,
        emailNotification: false,
        approachingAlert: false,
        violateAlert: true
      }
    };
    
    onChange(defaultSettings);
  };

    // CSS classes for sidebar items
    const getSidebarItemClass = (tabName) => {
        return `cursor-pointer ${activeTab === tabName ? 'bg-blue-900' : ''}`;
      };
    
  return (
    <div className="flex bg-white">
      {/* Sidebar */}
      <div className="w-64 bg-gray-800 text-white">
      <div 
          className={`p-4 border-b border-gray-700 ${getSidebarItemClass('speedCam')}`}
          onClick={() => setActiveTab('speedCam')}
        >
          <div className="flex items-center justify-between py-2">
            <span>Speed Camera Violation</span>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={settings.speedCamEnable}
                onChange={() => handleSidebarToggle('speedCam')}
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
            </label>
          </div>
        </div>

        <div 
          className={`p-4 border-b border-gray-700 ${getSidebarItemClass('rollingStop')}`}
          onClick={() => setActiveTab('rollingStop')}
        >
          <div className="flex items-center justify-between py-2">
            <span>Rolling Stop</span>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={settings.rollingStopEnable}
                onChange={() => handleSidebarToggle('rollingStop')}
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
            </label>
          </div>
        </div>

        <div 
          className={`p-4 border-b border-gray-700 ${getSidebarItemClass('railroadCrossing')}`}
          onClick={() => setActiveTab('railroadCrossing')}
        >
          <div className="flex items-center justify-between py-2">
            <span>Railroad Crossing Violation</span>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={settings.railroadCrossEnable}
                onChange={() => handleSidebarToggle('railroadCrossing')}
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
            </label>
          </div>
        </div>

        <div 
          className={`p-4 border-b border-gray-700 ${getSidebarItemClass('schoolZoneSpeed')}`}
          onClick={() => setActiveTab('schoolZoneSpeed')}
        >
          <div className="flex items-center justify-between py-2">
            <span>School Zone Speed Violation</span>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={settings.schoolZoneSpeedingEnable}
                onChange={() => handleSidebarToggle('schoolZoneSpeed')}
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
            </label>
          </div>
        </div>
      
      </div>

      {/* Main Content */}
      <div className="flex-1 p-6 text-gray-800">
      {activeTab === 'speedCam' && (
          <>
        <div className="flex items-center mb-4">
          <h3 className="text-lg font-medium mr-2">Speed Threshold:</h3>
          <Info size={16} className="text-gray-500" />
        </div>

        <div className="mb-6">
          <div className="relative inline-block w-64">
            <select
              value={`above ${getValue('speedCam_speed')} kph ( ${(getValue('speedCam_speed') * 0.621371).toFixed(1)} mph )`}
              onChange={(e) => {
                // Extract the number from the string
                const match = e.target.value.match(/above (\d+) kph/);
                if (match && match[1]) {
                  handleNumberChange('speedCam_speed', match[1]);
                }
              }}
              className="block w-full py-2 px-3 text-gray-700 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              {/* <option>{`above ${getValue('speedThreshold')} kph ( ${(getValue('speedThreshold') * 0.621371).toFixed(1)} mph )`}</option> */}
              <option>above 5 kph ( 3.1 mph )</option>
              <option>above 10 kph ( 6.2 mph )</option>
              <option>above 15 kph ( 9.3 mph )</option>
              <option>above 20 kph ( 12.4 mph )</option>
              <option>above 25 kph ( 15.5 mph )</option>
              <option>above 30 kph ( 18.6 mph )</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-white">
              <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
              </svg>
            </div>
          </div>
        </div>
        </>
        )}
         {(activeTab === 'speedCam' || activeTab === 'rollingStop') && (
        <div className="mb-8">
          <h3 className="text-lg font-medium mb-2">Event Recording:</h3>
          <p className="text-sm text-gray-500 mb-6">Set up the duration of the recorded video when the event is triggered, which will be captured by the device for reviewing.</p>
          
          <div className="mb-6">
            <label className="block text-sm font-medium mb-2">Pre-event video duration (sec):</label>
            <div className="relative inline-block">
              <input 
                type="text" 
                value={getValue(activeTab+'_videoBefore')} 
                onChange={(e) => handleNumberChange(activeTab+'_videoBefore', e.target.value)}
                className="block w-24 py-2 px-3 text-gray-700 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
              <div className="absolute inset-y-0 right-0 flex flex-col h-full">
                <button 
                  className="h-1/2 px-2 bg-gray-200 hover:bg-gray-300 rounded-tr-md border-l border-gray-300"
                  onClick={() => handleNumberChange(activeTab+'_videoBefore', (getValue(activeTab+'_videoBefore') + 1).toString())}
                >
                  ▲
                </button>
                <button 
                  className="h-1/2 px-2 bg-gray-200 hover:bg-gray-300 rounded-br-md border-l border-t border-gray-300"
                  onClick={() => handleNumberChange(activeTab+'_videoBefore', Math.max(0, getValue(activeTab+'_videoBefore') - 1).toString())}
                >
                  ▼
                </button>
              </div>
            </div>
          </div>
          
          <div className="mb-8">
            <label className="block text-sm font-medium mb-2">Post-event video duration (sec):</label>
            <div className="relative inline-block">
              <input 
                type="text" 
                value={getValue(activeTab+'_videoAfter')} 
                onChange={(e) => handleNumberChange(activeTab+'_videoAfter', e.target.value)}
                className="block w-24 py-2 px-3 text-gray-700 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
              <div className="absolute inset-y-0 right-0 flex flex-col h-full">
                <button 
                  className="h-1/2 px-2 bg-gray-200 hover:bg-gray-300 rounded-tr-md border-l border-gray-300"
                  onClick={() => handleNumberChange(activeTab+'_videoAfter', (getValue(activeTab+'_videoAfter') + 1).toString())}
                >
                  ▲
                </button>
                <button 
                  className="h-1/2 px-2 bg-gray-200 hover:bg-gray-300 rounded-br-md border-l border-t border-gray-300"
                  onClick={() => handleNumberChange(activeTab+'_videoAfter', Math.max(0, getValue(activeTab+'_videoAfter') - 1).toString())}
                >
                  ▼
                </button>
              </div>
            </div>
          </div>
        </div>
         )}
 {(activeTab === 'rollingStop') && (
        
        <div className="mb-8">
          <h3 className="text-lg font-medium mb-2">Auto Upload to Portal:</h3>
          <p className="text-sm text-gray-500 mb-6">Choose what kind of data to auto upload to Events for reviewing if the event is triggered.</p>
         
            <div className="mb-6">
              <label className="block text-sm font-medium mb-2">Media:</label>
              <div className="relative inline-block w-64">
                <select
                  value={getValue('media')}
                  onChange={(e) => handleDropdownChange('media', e.target.value)}
                  className="block w-full py-2 px-3 text-gray-700 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="video">Video (default)</option>
                  <option value="snapshot">Snapshot</option>
                  <option value="none">None</option>
                </select>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-white">
                  <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                    <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                  </svg>
                </div>
              </div>
            </div>
            </div>
          )}

        {(activeTab === 'speedCam') && (
                 <div className="mb-8">
                 <h3 className="text-lg font-medium mb-2">Auto Upload to Portal:</h3>
                 <p className="text-sm text-gray-500 mb-6">Choose what kind of data to auto upload to Events for reviewing if the event is triggered.</p>
                
       
          <div className="bg-gray-100 p-4 rounded-lg">
            <div className="grid grid-cols-3 gap-4 mb-4">
              <div className="flex items-center">
                <span className="block text-sm font-medium text-black">Severity Levels</span>
                <Info size={16} className="text-gray-400 ml-1" />
              </div>
              <div>
                <span className="block text-sm font-medium text-black">Media</span>
              </div>
              <div>
                <span className="block text-sm font-medium text-black">G-sensor & GPS Data</span>
              </div>
            </div>
            
            <div className="grid grid-cols-3 gap-4 py-2 border-t border-gray-500">
              <div>
                <span className="block text-black">Minor</span>
              </div>
              <div>
                <div className="relative inline-block w-full">
                  <select
                    value={getValue('speedCam_minorMedia')}
                    onChange={(e) => handleDropdownChange('speedCam_minorMedia', e.target.value)}
                    className="block w-full py-2 px-3 text-gray-700 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="snapshot">Snapshot</option>
                    <option value="video">Video</option>
                  </select>
                  <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-white">
                    <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                      <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                    </svg>
                  </div>
                </div>
              </div>
              <div>
                <div className="inline-block px-3 py-1 bg-green-500 text-green-200 rounded-full text-sm">
                  On
                </div>
              </div>
            </div>
            
            <div className="grid grid-cols-3 gap-4 py-2">
              <div>
                <span className="block text-black">Moderate</span>
              </div>
              <div>
                <div className="relative inline-block w-full">
                  <select
                    value={getValue('speedCam_moderateMedia')}
                    onChange={(e) => handleDropdownChange('speedCam_moderateMedia', e.target.value)}
                    className="block w-full py-2 px-3 text-gray-700 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="snapshot">Snapshot</option>
                    <option value="video">Video</option>
                  </select>
                  <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-white">
                    <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                      <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                    </svg>
                  </div>
                </div>
              </div>
              <div>
                <div className="inline-block px-3 py-1 bg-green-500 text-green-200 rounded-full text-sm">
                  On
                </div>
              </div>
            </div>
            
            <div className="grid grid-cols-3 gap-4 py-2">
              <div>
                <span className="block text-black">Severe</span>
              </div>
              <div>
                <div className="relative inline-block w-full">
                  <select
                    value={getValue('speedCam_severeMedia')}
                    onChange={(e) => handleDropdownChange('speedCam_severeMedia', e.target.value)}
                    className="block w-full py-2 px-3 text-gray-700 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="video">Video</option>
                    <option value="snapshot">Snapshot</option>
                  </select>
                  <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-white">
                    <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                      <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                    </svg>
                  </div>
                </div>
              </div>
              <div>
                <div className="inline-block px-3 py-1 bg-green-500 text-green-200 rounded-full text-sm">
                  On
                </div>
              </div>
            </div>
          </div>
          </div>
        )}

{(activeTab === 'rollingStop' || activeTab === 'speedCam' ) && (
            <div className="mb-6">
              <label className="block text-sm font-medium mb-2">G-sensor & GPS Data:</label>
              <div className="inline-block px-3 py-1 bg-green-900 text-green-200 rounded-full text-sm">
                On
              </div>
            </div>
          )}
        
        {activeTab === 'speedCam' && (
        <div className="mb-6">
          <div className="flex items-center space-x-2 mb-2">
            <h3 className="text-lg font-medium">Email Notification:</h3>
            <Info size={16} className="text-gray-500" />
          </div>

         
          
          <div className="relative inline-block w-64">
            <select
              value={getValue(activeTab+'_emailNotify')}
              onChange={(e) => handleDropdownChange(activeTab+'_emailNotify', e.target.value)}
              className="block w-full py-2 px-3 text-gray-700 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="off">Off</option>
              <option value="all">All Levels</option>
              <option value="severe">Severe Only</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-white">
              <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
              </svg>
            </div>
          </div>
          </div>
          )} 
          {activeTab === 'rollingStop' && (
            <div className="mb-6">
            <div className="flex items-center space-x-2 mb-2">
              <h3 className="text-lg font-medium">Email Notification:</h3>
              <Info size={16} className="text-gray-500" />
            </div>
  
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={getValue(activeTab+'_emailNotify')}
                onChange={() => handleToggle(activeTab+'_emailNotify')}
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
            </label>
      </div>
          )}

        <div className="mb-6">
          <h3 className="text-lg font-medium mb-2">In-cabin Audio Alert:</h3>
          <p className="text-sm text-gray-500 mb-4">Set up if play the in-cabin audio alert for the condition.</p>
          
          <div className="mb-4">
            <div className="flex items-center justify-between py-2">
              <span className="text-gray-800">Approaching the traffic sign or signal:</span>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  className="sr-only peer"
                  checked={getValue(activeTab+'_audioAlert')}
                  onChange={() => handleToggle(activeTab+'_audioAlert')}
                />
                <div className="w-11 h-6 bg-gray-300 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
              </label>
            </div>
          </div>
          {(activeTab === 'rollingStop' || activeTab === 'speedCam' ) && (
          <div className="mb-4">
            <div className="flex items-center justify-between py-2">
              <span className="text-gray-800">Violate the traffic sign or signal:</span>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  className="sr-only peer"
                  checked={getValue(activeTab+'_audioPreWarning')}
                  onChange={() => handleToggle(activeTab+'_audioPreWarning')}
                />
                <div className="w-11 h-6 bg-gray-300 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
              </label>
            </div>
       
          </div>
               )}
        </div>
      </div>
    </div>
  );
};

export default TrafficSettings;