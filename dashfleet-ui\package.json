{"name": "dashfleet-ui", "version": "1.0.0", "description": "Advanced fleet management and vehicle tracking system", "private": true, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "start": "vite preview --port 3000", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,css,scss,json}\"", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "type-check": "tsc --noEmit", "deploy": "npm run build && vercel --prod", "deploy:preview": "npm run build && vercel"}, "dependencies": {"@react-google-maps/api": "^2.20.6", "@reduxjs/toolkit": "^2.6.1", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.1", "@types/leaflet": "^1.9.17", "axios": "^1.6.5", "date-fns": "^2.30.0", "jwt-decode": "^4.0.0", "leaflet": "^1.9.4", "leaflet.markercluster": "^1.5.3", "lodash": "^4.17.21", "lucide-react": "^0.309.0", "paho-mqtt": "^1.1.0", "react": "^18.2.0", "react-bootstrap": "^2.10.9", "react-dom": "^18.2.0", "react-hook-form": "^7.49.3", "react-redux": "^9.2.0", "react-router-dom": "^6.21.1", "react-toastify": "^11.0.5", "recharts": "^2.10.3", "redux-persist": "^6.0.0", "socket.io-client": "^4.7.3", "uuid": "^9.0.1", "webrtc-adapter": "^9.0.3", "zod": "^3.22.4"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@testing-library/jest-dom": "^6.2.0", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.2", "@types/express": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/lodash": "^4.14.202", "@types/node": "^20.10.6", "@types/react": "^18.2.47", "@types/react-dom": "^18.2.18", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.18.0", "@typescript-eslint/parser": "^6.18.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "express": "^5.1.0", "jsdom": "^23.0.1", "jsonwebtoken": "^9.0.2", "msw": "^2.0.12", "postcss": "^8.4.33", "prettier": "^3.1.1", "stripe": "^18.1.1", "tailwindcss": "^3.4.1", "typescript": "^5.3.3", "vite": "^5.0.11", "vite-plugin-svgr": "^4.2.0", "vitest": "^1.1.3"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"node": ">=18.0.0"}}