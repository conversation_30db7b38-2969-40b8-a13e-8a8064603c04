// src/components/configuration/DeviceSettings.tsx
import React, { useState } from 'react';
import { Info } from 'lucide-react';

interface DeviceSettingsProps {
  settings: Record<string, any>;
  onChange: (key: string, value: any) => void;
  modifiedValues: Record<string, any>;
}

const DeviceSettings: React.FC<DeviceSettingsProps> = ({
  settings,
  onChange,
  modifiedValues,
}) => {
  const [selectedTab, setSelectedTab] = useState('system');

  // Helper function to get the current value (from modified values or original settings)
  const getValue = (key: string) => {
    return modifiedValues[key] !== undefined ? modifiedValues[key] : settings[key];
  };

  // Handle toggle changes
  const handleToggle = (key: string) => {
    onChange(key, !getValue(key));
  };

  // Handle slider changes
  const handleSliderChange = (key: string, value: number) => {
    onChange(key, value);
  };

  // Handle select changes
  const handleSelectChange = (key: string, value: string) => {
    onChange(key, value);
  };

  return (
    <div className="grid grid-cols-12 gap-6">
      {/* Left side - tabs */}
      <div className="col-span-3 bg-gray-800 text-white rounded-lg">
        <div 
          className={`p-4 cursor-pointer ${selectedTab === 'system' ? 'bg-blue-800' : ''}`}
          onClick={() => setSelectedTab('system')}
        >
          <h3 className="font-bold">System</h3>
        </div>
        <div 
          className={`p-4 cursor-pointer border-t border-gray-700 ${selectedTab === 'videoRecording' ? 'bg-blue-800' : ''}`}
          onClick={() => setSelectedTab('videoRecording')}
        >
          <h3 className="font-bold">Video Recording</h3>
        </div>
      </div>

      {/* Right side - configuration */}
      <div className="col-span-9 bg-white text-gray-800 rounded-lg p-6 shadow-md">
        {selectedTab === 'system' && renderSystemSettings()}
        {selectedTab === 'videoRecording' && <VideoRecordingSettings settings={settings} onChange={onChange} modifiedValues={modifiedValues} />}
      </div>
    </div>
  );

  function renderSystemSettings() {
    return (
      <div>
        <div className="mb-8">
          <label className="block text-lg font-medium mb-4">Volume:</label>
          <div className="mb-6">
            <div className="relative mt-6 mb-2">
              <input 
                type="range" 
                min="0" 
                max="5" 
                step="1"
                value={getValue('system_soundVolume')}
                onChange={(e) => handleSliderChange('system_soundVolume', parseInt(e.target.value, 10))}
                className="w-full h-2 bg-gray-300 rounded-lg appearance-none cursor-pointer"
              />
              <div className="flex justify-between text-sm text-gray-600 mt-1">
                <span>Mute</span>
                <span>1</span>
                <span>2</span>
                <span>3</span>
                <span>4</span>
                <span>5</span>
              </div>
            </div>
          </div>
        </div>

        <div className="mb-6">
          <label className="block text-lg font-medium mb-2">Speed Unit:</label>
          <div className="w-32">
            <select
            // src/components/configuration/DeviceSettings.tsx (continued)
            value={getValue('system_speedUnit')}
            onChange={(e) => handleSelectChange('system_speedUnit', e.target.value)}
            className="block w-full py-2 px-3 text-gray-700 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="mph">MPH</option>
            <option value="kph">KPH</option>
          </select>
        </div>
      </div>

      <div className="mb-6">
        <label className="block text-lg font-medium mb-2">Parking Photo:</label>
        <div className="w-32">
          <select
            value={getValue('system_parkingPhoto')}
            onChange={(e) => handleSelectChange('system_parkingPhoto', e.target.value)}
            className="block w-full py-2 px-3 text-gray-700 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="ALL">All</option>
            <option value="NONE">None</option>
            <option value="FRONT">Front Only</option>
          </select>
        </div>
      </div>

      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <label className="block text-lg font-medium">RFID Audio Hint:</label>
        </div>
        <label className="relative inline-flex items-center cursor-pointer">
          <input
            type="checkbox"
            className="sr-only peer"
            checked={getValue('system_rfid')}
            onChange={() => handleToggle('system_rfid')}
          />
          <div className="w-11 h-6 bg-gray-300 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
        </label>
      </div>

      <div className="mb-8">
        <label className="block text-lg font-medium mb-2">Audio Alert Language:</label>
        <div className="w-48">
          <select
            value={getValue('system_alertLanguage')}
            onChange={(e) => handleSelectChange('system_alertLanguage', e.target.value)}
            className="block w-full py-2 px-3 text-gray-700 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="en-US">English (US)</option>
            <option value="es-ES">Spanish</option>
            <option value="fr-FR">French</option>
            <option value="de-DE">German</option>
          </select>
        </div>
      </div>

      <div className="mt-8">
        <button 
          onClick={() => {
            // Reset all system settings to default
            const defaultSettings = {
              system_soundVolume: 4,
              system_speedUnit: 'mph',
              system_parkingPhoto: 'ALL',
              system_rfid: true,
              system_alertLanguage: 'en-US'
            };
            
            Object.entries(defaultSettings).forEach(([key, value]) => {
              onChange(key, value);
            });
          }}
          className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-full"
        >
          Restore to Default
        </button>
      </div>
    </div>
  );
}
};

// Sub-component for Video Recording tab
const VideoRecordingSettings: React.FC<DeviceSettingsProps> = ({
settings,
onChange,
modifiedValues,
}) => {
const [selectedCameraTab, setSelectedCameraTab] = useState('embedded');

// Helper function to get the current value
const getValue = (key: string) => {
  return modifiedValues[key] !== undefined ? modifiedValues[key] : settings[key];
};

// Handle toggle changes
const handleToggle = (key: string) => {
  onChange(key, !getValue(key));
};

return (
  <div>
    <div className="mb-6 flex items-center">
      <label className="text-lg font-medium mr-3">Video Snapshots:</label>
      <Info size={16} className="text-gray-500" />
      <div className="ml-auto">
        <label className="relative inline-flex items-center cursor-pointer">
          <input
            type="checkbox"
            className="sr-only peer"
            checked={getValue('vr_videoSnapshots')}
            onChange={() => handleToggle('vr_videoSnapshots')}
          />
          <div className="w-11 h-6 bg-gray-300 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
        </label>
      </div>
    </div>

    <div className="mb-8">
      <label className="block text-lg font-medium mb-4">Set Up Camera by model:</label>
      
      <div className="mb-4">
        <label className="block text-sm font-medium mb-2">Model:</label>
        <div className="w-48">
          <select
            className="block w-full py-2 px-3 text-gray-700 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="K145">K145/K145C</option>
            <option value="K245">K245/K245C</option>
            <option value="K220">K220</option>
            <option value="K165">K165</option>
            <option value="K265">K265</option>
          </select>
        </div>
      </div>

      <div className="border-b border-gray-300 mb-6">
        <div className="flex">
          <button
            className={`px-4 py-2 font-medium text-sm ${selectedCameraTab === 'embedded' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500'}`}
            onClick={() => setSelectedCameraTab('embedded')}
          >
            Embedded Camera
          </button>
          <button
            className={`px-4 py-2 font-medium text-sm ${selectedCameraTab === 'uvc' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500'}`}
            onClick={() => setSelectedCameraTab('uvc')}
          >
            UVC Camera
          </button>
        </div>
      </div>

      <div className="mb-6 flex justify-center">
        <img src="/assets/camera-device.png" alt="Camera device" className="h-32" />
      </div>

      <div className="mb-6">
        <label className="block text-lg font-medium mb-2">Video:</label>
        
        <div className="bg-white border border-gray-300 rounded-lg overflow-hidden">
          <table className="w-full text-gray-700">
            <thead>
              <tr className="border-b border-gray-300 bg-gray-100">
                <th className="px-4 py-3 text-left">Item</th>
                <th className="px-4 py-3 text-left">Value</th>
              </tr>
            </thead>
            <tbody>
              <tr className="border-b border-gray-300">
                <td className="px-4 py-3">Format</td>
                <td className="px-4 py-3">
                  <select
                    value={getValue('vr_videoFormat')}
                    onChange={(e) => onChange('vr_videoFormat', e.target.value)}
                    className="block w-full py-1 px-2 text-gray-700 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="Separate">Separate</option>
                    <option value="Side-By-Side">Side-By-Side</option>
                    <option value="Grid">Grid</option>
                    <option value="Single">Single</option>
                  </select>
                </td>
              </tr>
              <tr>
                <td className="px-4 py-3">Resolution</td>
                <td className="px-4 py-3">
                  <span className="block py-1 px-2">1280x720</span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div className="mt-4 flex justify-end">
          <div className="bg-gray-100 p-4 rounded-lg border border-gray-300">
            <div className="flex justify-between text-xs text-gray-500 mb-1">
              <span>1280</span>
            </div>
            <div className="bg-gray-200 border border-gray-300 w-32 h-24 flex items-center justify-center">
              <span className="text-sm text-gray-500">Outward</span>
            </div>
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span className="ml-auto">720</span>
            </div>
          </div>
        </div>
      </div>

      <div className="mb-6 flex items-center">
        <label className="text-lg font-medium mr-3">Speed & Coordinate Stamps:</label>
        <div className="ml-auto">
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              className="sr-only peer"
              checked={getValue('vr_speedCoordinateStamp')}
              onChange={() => handleToggle('vr_speedCoordinateStamp')}
            />
            <div className="w-11 h-6 bg-gray-300 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
          </label>
        </div>
      </div>

      <div className="mb-6">
        <div className="flex items-center mb-2">
          <label className="text-lg font-medium mr-2">EV Value:</label>
          <Info size={16} className="text-gray-500" />
        </div>
        
        <div className="bg-white border border-gray-300 rounded-lg overflow-hidden">
          <table className="w-full text-gray-700">
            <thead>
              <tr className="border-b border-gray-300 bg-gray-100">
                <th className="px-4 py-3 text-left">Camera</th>
                <th className="px-4 py-3 text-left">Value</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="px-4 py-3">Road-Facing Camera</td>
                <td className="px-4 py-3">
                  <select
                    value={getValue('vr_evOut')}
                    onChange={(e) => onChange('vr_evOut', parseInt(e.target.value))}
                    className="block w-full py-1 px-2 text-gray-700 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="0">0</option>
                    <option value="1">+1</option>
                    <option value="2">+2</option>
                    <option value="-1">-1</option>
                    <option value="-2">-2</option>
                  </select>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
);
};

export default DeviceSettings;