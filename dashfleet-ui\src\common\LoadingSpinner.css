/* src/components/common/LoadingSpinner.css */
.loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 2rem;
  }
  
  .loading-spinner.full-page {
    min-height: 50vh;
  }
  
  .loading-spinner .spinner-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  
  .loading-spinner .spinner-border {
    color: #0d6efd; /* Bootstrap primary color */
  }
  
  .loading-spinner p {
    margin-bottom: 0;
    color: #6c757d; /* Bootstrap secondary color */
    font-weight: 500;
  }
  
  /* Animation to make the spinner slightly pulse */
  @keyframes pulse {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.05);
      opacity: 0.8;
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }
  
  .loading-spinner .spinner-border {
    animation: spinner-border 0.75s linear infinite, pulse 2s ease-in-out infinite;
  }