# Environment Variables for DashFleet UI

# API Configuration (IMPORTANT: Your code uses VITE_APP_API_BASE_URL)
VITE_APP_API_BASE_URL=https://api.visionmaxfleet.com/V2
VITE_API_TIMEOUT=10000

# Google Maps API Key (IMPORTANT: Your code uses VITE_APP_GOOGLE_MAPS_API_KEY)
VITE_APP_GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here

# Stripe Configuration
VITE_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key_here

# WebSocket Configuration
VITE_WEBSOCKET_URL=wss://api.visionmaxfleet.com/ws

# MQTT Configuration
VITE_MQTT_BROKER_URL=wss://mqtt.visionmaxfleet.com:8084/mqtt
VITE_MQTT_USERNAME=your_mqtt_username
VITE_MQTT_PASSWORD=your_mqtt_password

# Application Configuration
VITE_APP_NAME=DashFleet UI
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=production

# Feature Flags (IMPORTANT: Your code uses these exact names)
VITE_APP_ENABLE_SOCKET_IO=true
VITE_APP_DEBUG_MODE=false

# AWS Configuration (if needed)
VITE_AWS_REGION=us-east-1
VITE_AWS_ACCESS_KEY_ID=your_aws_access_key
VITE_AWS_SECRET_ACCESS_KEY=your_aws_secret_key
