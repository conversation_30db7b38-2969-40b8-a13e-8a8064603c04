// src/components/configuration/SpeedingSettings.tsx
import React from 'react';
import ConfigField from './ConfigField';

interface SpeedingSettingsProps {
  settings: Record<string, any>;
  onChange: (key: string, value: any) => void;
  modifiedValues: Record<string, any>;
}

const SpeedingSettings: React.FC<SpeedingSettingsProps> = ({
  settings,
  onChange,
  modifiedValues,
}) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Speeding Detection</h3>
        <ConfigField
          label="Speeding Detection Enabled"
          id="speedingEnable"
          type="boolean"
          value={modifiedValues.speedingEnable !== undefined ? modifiedValues.speedingEnable : settings.speedingEnable}
          onChange={(value) => onChange('speedingEnable', value)}
          isModified={modifiedValues.speedingEnable !== undefined}
        />
        <ConfigField
          label="Truck Mode Enabled"
          id="truckModeEnable"
          type="boolean"
          value={modifiedValues.truckModeEnable !== undefined ? modifiedValues.truckModeEnable : settings.truckModeEnable}
          onChange={(value) => onChange('truckModeEnable', value)}
          isModified={modifiedValues.truckModeEnable !== undefined}
        />
        <ConfigField
          label="Speeding Threshold"
          id="speeding_threshold"
          type="number"
          value={modifiedValues.speeding_threshold !== undefined ? modifiedValues.speeding_threshold : settings.speeding_threshold}
          onChange={(value) => onChange('speeding_threshold', value)}
          isModified={modifiedValues.speeding_threshold !== undefined}
          description="Trigger threshold (km/h above limit)"
        />
      </div>
      
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Recording Settings</h3>
        <ConfigField
          label="Video Before (seconds)"
          id="speeding_videoBefore"
          type="number"
          value={modifiedValues.speeding_videoBefore !== undefined ? modifiedValues.speeding_videoBefore : settings.speeding_videoBefore}
          onChange={(value) => onChange('speeding_videoBefore', value)}
          isModified={modifiedValues.speeding_videoBefore !== undefined}
        />
        <ConfigField
          label="Video After (seconds)"
          id="speeding_videoAfter"
          type="number"
          value={modifiedValues.speeding_videoAfter !== undefined ? modifiedValues.speeding_videoAfter : settings.speeding_videoAfter}
          onChange={(value) => onChange('speeding_videoAfter', value)}
          isModified={modifiedValues.speeding_videoAfter !== undefined}
        />
        <ConfigField
          label="Audio Alert"
          id="speeding_audioAlert"
          type="boolean"
          value={modifiedValues.speeding_audioAlert !== undefined ? modifiedValues.speeding_audioAlert : settings.speeding_audioAlert}
          onChange={(value) => onChange('speeding_audioAlert', value)}
          isModified={modifiedValues.speeding_audioAlert !== undefined}
        />
      </div>
      
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Severity Settings</h3>
        <ConfigField
          label="Minor Violation Media Type"
          id="speeding_minorMedia"
          type="select"
          value={modifiedValues.speeding_minorMedia !== undefined ? modifiedValues.speeding_minorMedia : settings.speeding_minorMedia}
          onChange={(value) => onChange('speeding_minorMedia', value)}
          isModified={modifiedValues.speeding_minorMedia !== undefined}
          options={[
            { value: 'video', label: 'Video' },
            { value: 'snapshot', label: 'Snapshot' },
            { value: 'none', label: 'None' },
          ]}
        />
        <ConfigField
          label="Minor G-Sensor Recording"
          id="speeding_minorGsensor"
          type="boolean"
          value={modifiedValues.speeding_minorGsensor !== undefined ? modifiedValues.speeding_minorGsensor : settings.speeding_minorGsensor}
          onChange={(value) => onChange('speeding_minorGsensor', value)}
          isModified={modifiedValues.speeding_minorGsensor !== undefined}
        />
        <ConfigField
          label="Moderate Violation Media Type"
          id="speeding_moderateMedia"
          type="select"
          value={modifiedValues.speeding_moderateMedia !== undefined ? modifiedValues.speeding_moderateMedia : settings.speeding_moderateMedia}
          onChange={(value) => onChange('speeding_moderateMedia', value)}
          isModified={modifiedValues.speeding_moderateMedia !== undefined}
          options={[
            { value: 'video', label: 'Video' },
            { value: 'snapshot', label: 'Snapshot' },
            { value: 'none', label: 'None' },
          ]}
        />
        <ConfigField
          label="Moderate G-Sensor Recording"
          id="speeding_moderateGsensor"
          type="boolean"
          value={modifiedValues.speeding_moderateGsensor !== undefined ? modifiedValues.speeding_moderateGsensor : settings.speeding_moderateGsensor}
          onChange={(value) => onChange('speeding_moderateGsensor', value)}
          isModified={modifiedValues.speeding_moderateGsensor !== undefined}
        />
        <ConfigField
          label="Severe Violation Media Type"
          id="speeding_severeMedia"
          type="select"
          value={modifiedValues.speeding_severeMedia !== undefined ? modifiedValues.speeding_severeMedia : settings.speeding_severeMedia}
          onChange={(value) => onChange('speeding_severeMedia', value)}
          isModified={modifiedValues.speeding_severeMedia !== undefined}
          options={[
            { value: 'video', label: 'Video' },
            { value: 'snapshot', label: 'Snapshot' },
            { value: 'none', label: 'None' },
          ]}
        />
        <ConfigField
          label="Severe G-Sensor Recording"
          id="speeding_severeGsensor"
          type="boolean"
          value={modifiedValues.speeding_severeGsensor !== undefined ? modifiedValues.speeding_severeGsensor : settings.speeding_severeGsensor}
          onChange={(value) => onChange('speeding_severeGsensor', value)}
          isModified={modifiedValues.speeding_severeGsensor !== undefined}
        />
      </div>
      
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Truck Mode Settings</h3>
        <ConfigField
          label="Truck Mode Map Format"
          id="truckMode_MMF"
          type="select"
          value={modifiedValues.truckMode_MMF !== undefined ? modifiedValues.truckMode_MMF : settings.truckMode_MMF}
          onChange={(value) => onChange('truckMode_MMF', value)}
          isModified={modifiedValues.truckMode_MMF !== undefined}
          options={[
            { value: 'truck', label: 'Truck' },
            { value: 'normal', label: 'Normal' },
          ]}
        />
        <ConfigField
          label="MMF Overspeed Audio Alert"
          id="mmfOverSpeed_audioAlert"
          type="select"
          value={modifiedValues.mmfOverSpeed_audioAlert !== undefined ? modifiedValues.mmfOverSpeed_audioAlert : settings.mmfOverSpeed_audioAlert}
          onChange={(value) => onChange('mmfOverSpeed_audioAlert', value)}
          isModified={modifiedValues.mmfOverSpeed_audioAlert !== undefined}
          options={[
            { value: 'severe', label: 'Severe Only' },
            { value: 'moderate', label: 'Moderate & Severe' },
            { value: 'all', label: 'All Levels' },
            { value: 'off', label: 'Off' },
          ]}
        />
      </div>
    </div>
  );
};

export default SpeedingSettings;