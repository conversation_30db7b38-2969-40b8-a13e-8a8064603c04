/* src/components/common/LoadingState.css */
/* Common styles */
.loading-state {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
  }
  
  .loading-state .spinner-border {
    color: #0d6efd; /* Bootstrap primary blue */
  }
  
  .loading-state p {
    margin-bottom: 0;
  }
  
  .loading-state .error-icon {
    color: #dc3545; /* Bootstrap danger red */
    font-size: 2rem;
    margin-bottom: 1rem;
  }
  
  /* Inline loading state */
  .loading-state.inline {
    display: inline-flex;
    align-items: center;
  }
  
  /* Card loading state */
  .loading-state.card {
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  /* Overlay loading state */
  .loading-state.overlay-container {
    position: relative;
  }
  
  .loading-state .overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
  }
  
  .loading-state .overlay.error {
    background-color: rgba(255, 255, 255, 0.95);
  }
  
  .loading-state .spinner-container,
  .loading-state .error-container {
    text-align: center;
    padding: 1.5rem;
    border-radius: 0.5rem;
    background-color: rgba(255, 255, 255, 0.9);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  }
  
  /* Fullscreen loading state */
  .loading-state.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
  }
  
  .loading-state.fullscreen .spinner-container,
  .loading-state.fullscreen .error-container {
    text-align: center;
    padding: 2rem;
    border-radius: 0.5rem;
    background-color: white;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    max-width: 80%;
  }
  
  .loading-state.fullscreen .error-icon {
    font-size: 3rem;
  }
  
  /* Pulse animation for spinners */
  @keyframes pulse {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.05);
      opacity: 0.8;
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }
  
  .loading-state .spinner-border {
    animation: spinner-border 0.75s linear infinite, pulse 2s ease-in-out infinite;
  }