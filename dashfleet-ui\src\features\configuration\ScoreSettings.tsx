import React from 'react';
import { HelpTooltip } from '../../components/common/ToolTip';

interface ScoreSettingsProps {
  settings: Record<string, any>;
  onChange: (key: string, value: any) => void;
  modifiedValues: Record<string, any>;
}

const ScoreSettings: React.FC<ScoreSettingsProps> = ({
  settings,
  onChange,
  modifiedValues
}) => {
  // Define severity levels for configuration
  const severityLevels = [
    { label: 'Minor', value: 'minor' },
    { label: 'Moderate', value: 'moderate' },
    { label: 'Severe', value: 'severe' },
  ];

  // Helper function to get current value or default
const getValue = (key: string, defaultValue: number) => {
    console.log(settings);
    return modifiedValues[key] !== undefined ? modifiedValues[key] : settings[key];
  };
  
  // Helper function to handle dropdown changes
  const handleChange = (key: string, value: string) => {
    const numericValue = parseFloat(value);
    onChange(key, numericValue);
  };

  return (
    <div className="bg-white p-6 rounded-md">
      {/* Debug Panel - Remove in production */}
      {/* <div className="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded">
        <h4 className="font-medium text-yellow-800 mb-2">Debug Info:</h4>
        <p className="text-sm text-yellow-700">
          Settings object: {settings ? 'Exists' : 'Null/Undefined'}
        </p>
        <p className="text-sm text-yellow-700">
          Impact Value: {getValue('impactValue', 1)}
        </p>
        <p className="text-sm text-yellow-700">
          HA Value: {getValue('haValue', 1)}
        </p>
        <details className="mt-2">
          <summary className="text-sm text-yellow-700 cursor-pointer">View all settings</summary>
          <pre className="text-xs mt-2 bg-white p-2 rounded border">
            {JSON.stringify(settings, null, 2)}
          </pre>
        </details>                                   
      </div> */}

      <div className="mb-8">
        <h2 className="text-xl font-medium mb-2">Driver's Safety Score</h2>
        <p className="text-gray-600">Start with a safety score of 100.</p>
        <p className="text-gray-600">System starts calculating the new score after the driver has driven more than 5000 km.</p>
        <p className="text-gray-600">Safety score is calculated using a mix of sensor events (including impact and harsh driving), AI-detected events (including ADAS, DMS) and speeding events that are differently weighted.</p>
        <p className="text-gray-600">Safety score will be subtracted if an event is triggered. The more events are triggered by the driver, the lower the safety score the driver has.</p>
        <p className="text-gray-600">Only events that were triggered in the last 5000 km of trips are count.</p>
      </div>

      <div className="mb-8">
        <h3 className="text-lg font-medium mb-2">Equation</h3>
        <p className="text-gray-600">Safety Score = (1- (Σ Trip score) / 5000) * 100</p>
        <p className="text-gray-600">Trip score = TRUNCATE ((Event Score * 70 / 100) + (Overspeed Score * 30 / 100),2)</p>
        <p className="text-gray-600">Event Score = Event Score x Weight</p>
        <p className="text-gray-600">Overspeed Score = (Cumulative distance of Overspeed / Trip distance) * 100</p>
      </div>

      <div className="mb-8">
        <h3 className="text-lg font-medium mb-4">Configure the Calculation:</h3>
        
        {/* Event Weights Section */}
        <div className="mb-8 border rounded-md overflow-hidden">
          <div className="bg-gray-100 p-4">
            <div className="grid grid-cols-12 gap-4 items-center">
              <div className="col-span-3 font-medium">Event</div>
              <div className="col-span-2 font-medium">Event Score</div>
              <div className="col-span-1 font-medium flex items-center justify-center">
                Weights for Severity Levels
                <div className="ml-2">
                  <HelpTooltip 
                    content="Severity level weights multiply the base event score"
                    size="sm"
                  />
                </div>
              </div>
              <div className="col-span-6 grid grid-cols-5 gap-2 items-center">
                <div className="font-medium text-center">Minor</div>
                <div className="font-medium text-center">Moderate</div>
                <div className="font-medium text-center">Severe</div>
                <div></div>
                <div></div>
              </div>
            </div>
          </div>

          {/* IMPACT Section */}
          <div className="p-4 border-t border-gray-200">
            <div className="uppercase font-medium mb-4 text-gray-600">IMPACT</div>
            <div className="grid grid-cols-12 gap-4 items-center">
              <div className="col-span-3">Impact</div>
              <div className="col-span-2 flex items-center space-x-2">
                <span className="text-gray-600">−</span>
                <select 
                  className="p-2 border rounded border-gray-300 bg-white w-full"
                  value={getValue('impactValue', 1)}
                  onChange={(e) => handleChange('impactValue', e.target.value)}
                >
                  {[1, 2, 3, 4, 5].map(val => (
                    <option key={val} value={val}>{val}</option>
                  ))}
                </select>
              </div>
              <div className="col-span-7"></div>
            </div>
          </div>

          {/* HARSH DRIVING Section */}
          <div className="p-4 border-t border-gray-200">
            <div className="uppercase font-medium mb-4 text-gray-600">HARSH DRIVING</div>
            
            <div className="space-y-4">
              {/* Harsh Acceleration */}
              <div className="grid grid-cols-12 gap-4 items-center">
                <div className="col-span-3">Harsh Acceleration</div>
                <div className="col-span-2 flex items-center space-x-2">
                  <span className="text-gray-600">−</span>
                  <select 
                    className="p-2 border rounded border-gray-300 bg-white w-full"
                    value={getValue('haValue', 1)}
                    onChange={(e) => handleChange('haValue', e.target.value)}
                  >
                    {[1, 2, 3, 4, 5].map(val => (
                      <option key={val} value={val}>{val}</option>
                    ))}
                  </select>
                </div>
                <div className="col-span-1 text-center text-gray-600 font-medium">×</div>
                <div className="col-span-6 grid grid-cols-5 gap-2 items-center">
                  <select 
                    className="p-2 border rounded border-gray-300 bg-white w-full"
                    value={getValue('haMinorWeight', 1.5)}
                    onChange={(e) => handleChange('haMinorWeight', e.target.value)}
                  >
                    {[1, 1.5, 2, 2.5, 3].map(val => (
                      <option key={val} value={val}>{val}</option>
                    ))}
                  </select>
                  <span className="text-gray-500 text-sm text-center">or</span>
                  <select 
                    className="p-2 border rounded border-gray-300 bg-white w-full"
                    value={getValue('haModerateWeight', 2)}
                    onChange={(e) => handleChange('haModerateWeight', e.target.value)}
                  >
                    {[2, 3, 4].map(val => (
                      <option key={val} value={val}>{val}</option>
                    ))}
                  </select>
                  <span className="text-gray-500 text-sm text-center">or</span>
                  <select 
                    className="p-2 border rounded border-gray-300 bg-white w-full"
                    value={getValue('haSevereWeight', 4)}
                    onChange={(e) => handleChange('haSevereWeight', e.target.value)}
                  >
                    {[3, 4, 5].map(val => (
                      <option key={val} value={val}>{val}</option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Harsh Braking */}
              <div className="grid grid-cols-12 gap-4 items-center">
                <div className="col-span-3">Harsh Braking</div>
                <div className="col-span-2 flex items-center space-x-2">
                  <span className="text-gray-600">−</span>
                  <select 
                    className="p-2 border rounded border-gray-300 bg-white w-full"
                    value={getValue('hbValue', 1)}
                    onChange={(e) => handleChange('hbValue', e.target.value)}
                  >
                    {[1, 2, 3, 4, 5].map(val => (
                      <option key={val} value={val}>{val}</option>
                    ))}
                  </select>
                </div>
                <div className="col-span-1 text-center text-gray-600 font-medium">×</div>
                <div className="col-span-6 grid grid-cols-5 gap-2 items-center">
                  <select 
                    className="p-2 border rounded border-gray-300 bg-white w-full"
                    value={getValue('hbMinorWeight', 1.5)}
                    onChange={(e) => handleChange('hbMinorWeight', e.target.value)}
                  >
                    {[1, 1.5, 2, 2.5, 3].map(val => (
                      <option key={val} value={val}>{val}</option>
                    ))}
                  </select>
                  <span className="text-gray-500 text-sm text-center">or</span>
                  <select 
                    className="p-2 border rounded border-gray-300 bg-white w-full"
                    value={getValue('hbModerateWeight', 2)}
                    onChange={(e) => handleChange('hbModerateWeight', e.target.value)}
                  >
                    {[2, 3, 4].map(val => (
                      <option key={val} value={val}>{val}</option>
                    ))}
                  </select>
                  <span className="text-gray-500 text-sm text-center">or</span>
                  <select 
                    className="p-2 border rounded border-gray-300 bg-white w-full"
                    value={getValue('hbSevereWeight', 4)}
                    onChange={(e) => handleChange('hbSevereWeight', e.target.value)}
                  >
                    {[3, 4, 5].map(val => (
                      <option key={val} value={val}>{val}</option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Harsh Cornering */}
              <div className="grid grid-cols-12 gap-4 items-center">
                <div className="col-span-3">Harsh Cornering</div>
                <div className="col-span-2 flex items-center space-x-2">
                  <span className="text-gray-600">−</span>
                  <select 
                    className="p-2 border rounded border-gray-300 bg-white w-full"
                    value={getValue('hcValue', 1)}
                    onChange={(e) => handleChange('hcValue', e.target.value)}
                  >
                    {[1, 2, 3, 4, 5].map(val => (
                      <option key={val} value={val}>{val}</option>
                    ))}
                  </select>
                </div>
                <div className="col-span-1 text-center text-gray-600 font-medium">×</div>
                <div className="col-span-6 grid grid-cols-5 gap-2 items-center">
                  <select 
                    className="p-2 border rounded border-gray-300 bg-white w-full"
                    value={getValue('hcMinorWeight', 1.5)}
                    onChange={(e) => handleChange('hcMinorWeight', e.target.value)}
                  >
                    {[1, 1.5, 2, 2.5, 3].map(val => (
                      <option key={val} value={val}>{val}</option>
                    ))}
                  </select>
                  <span className="text-gray-500 text-sm text-center">or</span>
                  <select 
                    className="p-2 border rounded border-gray-300 bg-white w-full"
                    value={getValue('hcModerateWeight', 2)}
                    onChange={(e) => handleChange('hcModerateWeight', e.target.value)}
                  >
                    {[2, 3, 4].map(val => (
                      <option key={val} value={val}>{val}</option>
                    ))}
                  </select>
                  <span className="text-gray-500 text-sm text-center">or</span>
                  <select 
                    className="p-2 border rounded border-gray-300 bg-white w-full"
                    value={getValue('hcSevereWeight', 4)}
                    onChange={(e) => handleChange('hcSevereWeight', e.target.value)}
                  >
                    {[3, 4, 5].map(val => (
                      <option key={val} value={val}>{val}</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          </div>

          {/* ADAS Section */}
          <div className="p-4 border-t border-gray-200">
            <div className="uppercase font-medium mb-4 text-gray-600">ADAS</div>
            <div className="space-y-4">
              {/* Frontal Collision Warning */}
              <div className="grid grid-cols-12 gap-4 items-center">
                <div className="col-span-3">Frontal Collision Warning</div>
                <div className="col-span-2 flex items-center space-x-2">
                  <span className="text-gray-600">−</span>
                  <select 
                    className="p-2 border rounded border-gray-300 bg-white w-full"
                    value={getValue('frontalValue', 1)}
                    onChange={(e) => handleChange('frontalValue', e.target.value)}
                  >
                    {[1, 2, 3, 4, 5].map(val => (
                      <option key={val} value={val}>{val}</option>
                    ))}
                  </select>
                </div>
                <div className="col-span-7"></div>
              </div>

              {/* Tailgating */}
              <div className="grid grid-cols-12 gap-4 items-center">
                <div className="col-span-3">Tailgating</div>
                <div className="col-span-2 flex items-center space-x-2">
                  <span className="text-gray-600">−</span>
                  <select 
                    className="p-2 border rounded border-gray-300 bg-white w-full"
                    value={getValue('tailgatingValue', 1)}
                    onChange={(e) => handleChange('tailgatingValue', e.target.value)}
                  >
                    {[1, 2, 3, 4, 5].map(val => (
                      <option key={val} value={val}>{val}</option>
                    ))}
                  </select>
                </div>
                <div className="col-span-7"></div>
              </div>

              {/* Lane Departure */}
              <div className="grid grid-cols-12 gap-4 items-center">
                <div className="col-span-3">Lane Departure</div>
                <div className="col-span-2 flex items-center space-x-2">
                  <span className="text-gray-600">−</span>
                  <select 
                    className="p-2 border rounded border-gray-300 bg-white w-full"
                    value={getValue('laneValue', 1)}
                    onChange={(e) => handleChange('laneValue', e.target.value)}
                  >
                    {[1, 2, 3, 4, 5].map(val => (
                      <option key={val} value={val}>{val}</option>
                    ))}
                  </select>
                </div>
                <div className="col-span-7"></div>
              </div>

              {/* Stop and Go */}
              <div className="grid grid-cols-12 gap-4 items-center">
                <div className="col-span-3">Stop and Go</div>
                <div className="col-span-2 flex items-center space-x-2">
                  <span className="text-gray-600">−</span>
                  <select 
                    className="p-2 border rounded border-gray-300 bg-white w-full"
                    value={getValue('stopandgoValue', 1)}
                    onChange={(e) => handleChange('stopandgoValue', e.target.value)}
                  >
                    {[1, 2, 3, 4, 5].map(val => (
                      <option key={val} value={val}>{val}</option>
                    ))}
                  </select>
                </div>
                <div className="col-span-7"></div>
              </div>
            </div>
          </div>

          {/* DMS Section */}
          <div className="p-4 border-t border-gray-200">
            <div className="uppercase font-medium mb-4 text-gray-600">DMS</div>
            <div className="space-y-4">
              {/* Distracted */}
              <div className="grid grid-cols-12 gap-4 items-center">
                <div className="col-span-3">Distracted</div>
                <div className="col-span-2 flex items-center space-x-2">
                  <span className="text-gray-600">−</span>

                  <select 
                    className="p-2 border rounded border-gray-300 bg-white w-20"
                    value={getValue('distractionValue', 2)}
                    onChange={(e) => handleChange('distractionValue', e.target.value)}
                  >
                    {[1, 2, 3, 4, 5].map(val => (
                      <option key={val} value={val}>{val}</option>
                    ))}
                  </select>
                </div>
                <div className="col-span-7"></div>
              </div>

              {/* Phone Usage */}
              <div className="grid grid-cols-12 gap-4 items-center">
                <div className="col-span-3">Phone Usage</div>
                <div className="col-span-2 flex items-center space-x-2">
                  <span className="text-gray-600">−</span>
                  <select 
                    className="p-2 border rounded border-gray-300 bg-white w-20"
                    value={getValue('phoneValue', 2)}
                    onChange={(e) => handleChange('phoneValue', e.target.value)}
                  >
                    {[1, 2, 3, 4, 5].map(val => (
                      <option key={val} value={val}>{val}</option>
                    ))}
                  </select>
                </div>
            <div className="col-span-7"></div>
              </div>
            </div>
          </div>

          {/* TRAFFIC SIGNS AND SIGNALS Section */}
          <div className="p-4 border-t border-gray-200">
          <div className="uppercase font-medium mb-4 text-gray-600">TRAFFIC SIGNS AND SIGNALS</div>
            <div className="space-y-4">
              {/* Speed Camera Violation */}
              <div className="grid grid-cols-12 gap-4 items-center">
                <div className="col-span-3">Speed Camera Violation</div>
                <div className="col-span-2 flex items-center space-x-2">
                <span className="text-gray-600">−</span>
                  <select 
                    className="p-2 border rounded border-gray-300 bg-white w-20"
                    value={getValue('speedCameraValue', 1)}
                    onChange={(e) => handleChange('speedCameraValue', e.target.value)}
                  >
                    {[1, 2, 3, 4, 5].map(val => (
                      <option key={val} value={val}>{val}</option>
                    ))}
                  </select>
                </div>
                <div className="col-span-1 text-center text-gray-600 font-medium">×</div>
                <div className="col-span-6 grid grid-cols-5 gap-2 items-center">
                <select 
                    className="p-2 border rounded border-gray-300 bg-white w-16"
                    value={getValue('speedCameraMinorWeight', 1)}
                    onChange={(e) => handleChange('speedCameraMinorWeight', e.target.value)}
                  >
                    {[1, 1.25, 1.5].map(val => (
                      <option key={val} value={val}>{val}</option>
                    ))}
                  </select>
                  <span className="text-gray-500 text-sm text-center">or</span>
                  <select 
                    className="p-2 border rounded border-gray-300 bg-white w-16"
                    value={getValue('speedCameraModerateWeight', 1.5)}
                    onChange={(e) => handleChange('speedCameraModerateWeight', e.target.value)}
                  >
                    {[1.5, 2, 2.5].map(val => (
                      <option key={val} value={val}>{val}</option>
                    ))}
                  </select>
                  <span className="text-gray-500 text-sm text-center">or</span>
                  <select 
                    className="p-2 border rounded border-gray-300 bg-white w-16"
                    value={getValue('speedCameraSevereWeight', 3)}
                    onChange={(e) => handleChange('speedCameraSevereWeight', e.target.value)}
                  >
                    {[2, 3, 4].map(val => (
                      <option key={val} value={val}>{val}</option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Rolling Stop */}
              <div className="grid grid-cols-12 gap-4 items-center">
                <div className="col-span-3">Rolling Stop</div>
                <div className="col-span-2 flex items-center space-x-2">
                <span className="text-gray-600">−</span>
                  <select 
                    className="p-2 border rounded border-gray-300 bg-white w-20"
                    value={getValue('rollingStopValue', 1)}
                    onChange={(e) => handleChange('rollingStopValue', e.target.value)}
                  >
                    {[1, 2, 3, 4, 5].map(val => (
                      <option key={val} value={val}>{val}</option>
                    ))}
                  </select>
                </div>

              </div>
            </div>
          </div>
        </div>

        {/* Driver Safety Levels */}
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <h3 className="text-lg font-medium">Driver Safety Levels:</h3>
            <div className="ml-2">
              <HelpTooltip 
                content="Score ranges for different safety levels"
                size="sm"
              />
            </div>
          </div>
          
          <div className="border rounded-md overflow-hidden">
            <div className="grid grid-cols-2 bg-gray-100 p-4">
              <div className="font-medium">Driver Safety Levels</div>
              <div className="font-medium">Score Range</div>
            </div>
            
            <div className="grid grid-cols-2 p-4 border-t border-gray-200">
              <div>Excellent</div>
              <div>95 +</div>
            </div>
            
            <div className="grid grid-cols-2 p-4 border-t border-gray-200">
              <div>Good</div>
              <div>85 – 94</div>
            </div>
            
            <div className="grid grid-cols-2 p-4 border-t border-gray-200">
              <div>Normal</div>
              <div>70 – 84</div>
            </div>
            
            <div className="grid grid-cols-2 p-4 border-t border-gray-200">
              <div>Poor</div>
              <div>or lower</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScoreSettings;