// src/components/common/LoadingState.tsx
import React from 'react';
import { Spinner, Card, Button } from 'react-bootstrap';
import './LoadingState.css';

type LoadingStateType = 'inline' | 'overlay' | 'card' | 'button' | 'fullscreen';

interface LoadingStateProps {
  type?: LoadingStateType;
  text?: string;
  isLoading: boolean;
  error?: string | null;
  onRetry?: () => void;
  children?: React.ReactNode;
}

const LoadingState: React.FC<LoadingStateProps> = ({
  type = 'card',
  text = 'Loading...',
  isLoading,
  error = null,
  onRetry,
  children
}) => {
  // If not loading and no error, render children
  if (!isLoading && !error) {
    return <>{children}</>;
  }

  // Inline loading spinner
  if (type === 'inline') {
    return (
      <div className="loading-state inline">
        {isLoading ? (
          <div className="d-flex align-items-center">
            <Spinner animation="border" size="sm" />
            <span className="ms-2">{text}</span>
          </div>
        ) : (
          <div className="text-danger d-flex align-items-center">
            <i className="bi bi-exclamation-circle me-2"></i>
            <span>{error}</span>
            {onRetry && (
              <Button 
                variant="link" 
                size="sm"
                className="ms-2 p-0"
                onClick={onRetry}
              >
                Retry
              </Button>
            )}
          </div>
        )}
      </div>
    );
  }

  // Button with loading state
  if (type === 'button') {
    return (
      <Button 
        disabled={isLoading}
        onClick={onRetry}
        variant={error ? "danger" : "primary"}
      >
        {isLoading ? (
          <>
            <Spinner
              as="span"
              animation="border"
              size="sm"
              role="status"
              aria-hidden="true"
              className="me-2"
            />
            {text}
          </>
        ) : error ? (
          <>
            <i className="bi bi-exclamation-circle me-2"></i>
            Error - Retry
          </>
        ) : (
          children
        )}
      </Button>
    );
  }

  // Overlay loading spinner (for contained components)
  if (type === 'overlay') {
    return (
      <div className="loading-state overlay-container">
        {children}
        
        {isLoading && (
          <div className="overlay">
            <div className="spinner-container">
              <Spinner animation="border" />
              <p className="mt-2">{text}</p>
            </div>
          </div>
        )}
        
        {error && !isLoading && (
          <div className="overlay error">
            <div className="error-container">
              <i className="bi bi-exclamation-circle error-icon"></i>
              <p>{error}</p>
              {onRetry && (
                <Button 
                  variant="primary"
                  onClick={onRetry}
                  size="sm"
                >
                  Retry
                </Button>
              )}
            </div>
          </div>
        )}
      </div>
    );
  }

  // Fullscreen loading spinner
  if (type === 'fullscreen') {
    return (
      <div className="loading-state fullscreen">
        {isLoading ? (
          <div className="spinner-container">
            <Spinner animation="border" />
            <p className="mt-3">{text}</p>
          </div>
        ) : (
          <div className="error-container">
            <i className="bi bi-exclamation-circle error-icon"></i>
            <p>{error}</p>
            {onRetry && (
              <Button 
                variant="primary"
                onClick={onRetry}
                className="mt-3"
              >
                Retry
              </Button>
            )}
          </div>
        )}
      </div>
    );
  }

  // Card loading spinner (default)
  return (
    <Card className="loading-state card">
      <Card.Body className="text-center p-5">
        {isLoading ? (
          <>
            <Spinner animation="border" />
            <p className="mt-3 mb-0">{text}</p>
          </>
        ) : (
          <>
            <i className="bi bi-exclamation-circle error-icon"></i>
            <p className="mt-3 mb-3">{error}</p>
            {onRetry && (
              <Button 
                variant="primary"
                onClick={onRetry}
              >
                Retry
              </Button>
            )}
          </>
        )}
      </Card.Body>
    </Card>
  );
};

export default LoadingState;