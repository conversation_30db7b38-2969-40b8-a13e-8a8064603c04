/// <reference types="vite/client" />

interface ImportMetaEnv {
  // API Configuration
  readonly VITE_APP_API_BASE_URL: string;
  readonly VITE_API_TIMEOUT: string;

  // Google Maps
  readonly VITE_APP_GOOGLE_MAPS_API_KEY: string;

  // Stripe
  readonly VITE_STRIPE_PUBLISHABLE_KEY: string;

  // WebSocket & MQTT
  readonly VITE_WEBSOCKET_URL: string;
  readonly VITE_MQTT_BROKER_URL: string;
  readonly VITE_MQTT_USERNAME: string;
  readonly VITE_MQTT_PASSWORD: string;

  // Application
  readonly VITE_APP_NAME: string;
  readonly VITE_APP_VERSION: string;
  readonly VITE_APP_ENVIRONMENT: string;

  // Feature Flags
  readonly VITE_APP_ENABLE_SOCKET_IO: string;
  readonly VITE_APP_DEBUG_MODE: string;

  // AWS (if needed)
  readonly VITE_AWS_REGION: string;
  readonly VITE_AWS_ACCESS_KEY_ID: string;
  readonly VITE_AWS_SECRET_ACCESS_KEY: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}