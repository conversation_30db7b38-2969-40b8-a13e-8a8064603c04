// src/pages/ConfigurationPage.tsx
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger, Ta<PERSON>Content } from '../../components/common/Tabs';
import { Card, CardHeader, CardTitle, CardContent } from '../../components/common/Card';
import { Button } from '../../components/common/Button';
import { Alert } from '../../components/common/Alert';
import { Spinner } from '../../components/common/Spinner';
import { VisionMaxAPI } from '../../api/visionMaxApi';

// Import configuration form components
import DeviceSettings from '../configuration/DeviceSettings';
import SensorSettings from '../configuration/SensorSettings';
import AIEventSettings from '../configuration/AIEventSettings';
import SpeedingSettings from '../configuration/SpeedingSettings';
import TrafficSettings from '../configuration/TrafficSettings';
import ScoreSettings from '../configuration/ScoreSettings';

const ConfigurationPage = () => {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('device');
  
  // Store the original configuration data
  const [originalConfig, setOriginalConfig] = useState<any>(null);
  
  // Store the modified configuration values
  const [modifiedValues, setModifiedValues] = useState<any>({});
  
  // Create API instance - should be properly initialized elsewhere in your application
  const api = new VisionMaxAPI({
    baseURL: 'https://api.visionmaxfleet.com',
    token: localStorage.getItem('authToken') || ''
  });
  
  useEffect(() => {
    // Fetch configuration data on component mount
    const fetchConfiguration = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await api.getFullConfiguration();
        if (response && response.data && response.data.current && response.data.current.heavy_duty) {
          setOriginalConfig(response.data.current.heavy_duty);
        } else {
          throw new Error('Invalid configuration data structure');
        }
      } catch (err) {
        setError('Failed to load configuration: ' + (err instanceof Error ? err.message : String(err)));
      } finally {
        setLoading(false);
      }
    };
    
    fetchConfiguration();
  }, []);
  
  // Handle value changes
  const handleValueChange = (section: string, key: string, value: any) => {
    setModifiedValues(prev => ({
      ...prev,
      [section]: {
        ...(prev[section] || {}),
        [key]: value
      }
    }));
  };
  
  // Prepare data for saving (only the modified values)
  const prepareDataForSaving = () => {
    const payload: any = {};
    
    if (Object.keys(modifiedValues).length > 0) {
      payload.heavy_duty = modifiedValues;
    }
    
    return payload;
  };
  
  // Save configuration changes
  const handleSave = async () => {
    try {
      setSaving(true);
      setError(null);
      setSuccess(null);
      
      const payload = prepareDataForSaving();
      
      if (Object.keys(payload).length === 0) {
        setSuccess('No changes to save');
        return;
      }
      
      const response = await api.updateConfiguration(payload);
      
      if (response && response.message === 'Success') {
        setSuccess('Configuration saved successfully');
        setModifiedValues({});
        
        // Refresh configuration data
        const updatedConfig = await api.getFullConfiguration();
        if (updatedConfig && updatedConfig.data && updatedConfig.data.current && updatedConfig.data.current.heavy_duty) {
          setOriginalConfig(updatedConfig.data.current.heavy_duty);
        }
      } else {
        throw new Error('Failed to save configuration');
      }
    } catch (err) {
      setError('Failed to save configuration: ' + (err instanceof Error ? err.message : String(err)));
    } finally {
      setSaving(false);
    }
  };
  
  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };
  
  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Spinner size="large" />
      </div>
    );
  }
  
  if (!originalConfig) {
    return (
      <div className="p-6">
        <Alert 
          type="error" 
          title="Configuration Error" 
          message="Failed to load configuration data. Please try again later."
        />
        <Button 
          className="mt-4" 
          onClick={() => window.location.reload()}
        >
          Reload Page
        </Button>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Vehicle Configuration</h1>
        <p className="text-gray-600 mt-2">
          Configure settings for heavy duty vehicles
        </p>
      </div>
      
      {error && (
        <Alert 
          type="error" 
          title="Error" 
          message={error} 
          className="mb-6"
          closable
          onClose={() => setError(null)}
        />
      )}
      
      {success && (
        <Alert 
          type="success" 
          title="Success" 
          message={success} 
          className="mb-6"
          closable
          onClose={() => setSuccess(null)}
        />
      )}
      
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Heavy Duty Vehicle Configuration</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={handleTabChange}>
            <TabsList className="mb-6">
              <TabsTrigger value="device">Device</TabsTrigger>
              <TabsTrigger value="sensor">Sensor</TabsTrigger>
              <TabsTrigger value="aiEventDetection">AI Event Detection</TabsTrigger>
              <TabsTrigger value="speeding">Speeding</TabsTrigger>
              <TabsTrigger value="trafficAndSign">Traffic & Signs</TabsTrigger>
              <TabsTrigger value="score">Scores</TabsTrigger>
            </TabsList>
            
            <TabsContent value="device">
              <DeviceSettings 
                settings={originalConfig.device} 
                onChange={(key, value) => handleValueChange('device', key, value)}
                modifiedValues={modifiedValues.device || {}}
              />
            </TabsContent>
            
            <TabsContent value="sensor">
              <SensorSettings 
                settings={originalConfig.sensor} 
                onChange={(key, value) => handleValueChange('sensor', key, value)}
                modifiedValues={modifiedValues.sensor || {}}
              />
            </TabsContent>
            
            <TabsContent value="aiEventDetection">
              <AIEventSettings 
                settings={originalConfig.aiEventDetection} 
                onChange={(key, value) => handleValueChange('aiEventDetection', key, value)}
                modifiedValues={modifiedValues.aiEventDetection || {}}
              />
            </TabsContent>
            
            <TabsContent value="speeding">
              <SpeedingSettings 
                settings={originalConfig.speeding} 
                onChange={(key, value) => handleValueChange('speeding', key, value)}
                modifiedValues={modifiedValues.speeding || {}}
              />
            </TabsContent>
            
            <TabsContent value="trafficAndSign">
              <TrafficSettings 
                settings={originalConfig.trafficAndSign} 
                onChange={(key, value) => handleValueChange('trafficAndSign', key, value)}
                modifiedValues={modifiedValues.trafficAndSign || {}}
              />
            </TabsContent>
            
            <TabsContent value="score">
              <ScoreSettings 
                settings={originalConfig.score} 
                onChange={(key, value) => handleValueChange('score', key, value)}
                modifiedValues={modifiedValues.score || {}}
              />
            </TabsContent>
          </Tabs>
          
          <div className="flex justify-end mt-6">
            <Button 
              variant="outline" 
              className="mr-2"
              onClick={() => setModifiedValues({})}
              disabled={Object.keys(modifiedValues).length === 0 || saving}
            >
              Cancel
            </Button>
            <Button 
              onClick={handleSave}
              isLoading={saving}
              disabled={Object.keys(modifiedValues).length === 0 || saving}
            >
              Save Changes
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ConfigurationPage;