// src/components/configuration/AIEventSettings.tsx
import React, { useState } from 'react';
import { Info } from 'lucide-react';

interface AIEventSettingsProps {
  settings: Record<string, any>;
  onChange: (key: string, value: any) => void;
  modifiedValues: Record<string, any>;
}

const AIEventSettings: React.FC<AIEventSettingsProps> = ({
  settings,
  onChange,
  modifiedValues,
}) => {
  const [selectedFeature, setSelectedFeature] = useState('frontal');

  // Helper function to get the current value (from modified values or original settings)
  const getValue = (key: string) => {
    return modifiedValues[key] !== undefined ? modifiedValues[key] : settings[key];
  };

  // Handle toggle changes
  const handleToggle = (key: string) => {
    onChange(key, !getValue(key));
  };

  // Handle slider changes
  const handleSliderChange = (key: string, value: number) => {
    onChange(key, value);
  };

  // Handle input number changes
  const handleNumberChange = (key: string, value: string) => {
    const numValue = parseInt(value, 10);
    if (!isNaN(numValue)) {
      onChange(key, numValue);
    }
  };

  // Handle select changes
  const handleSelectChange = (key: string, value: string) => {
    onChange(key, value);
  };

  return (
    <div className="grid grid-cols-12 gap-6">
      {/* Left side - toggles */}
      <div className="col-span-3 bg-gray-800 text-white rounded-lg">
        <div className="p-4 border-b border-gray-700">
          <h3 className="font-bold">ADAS</h3>
          <p className="text-xs text-gray-400 mt-1">
            Available in vehicles with devices applying the Advanced or Pro plan
          </p>
        </div>

        {/* ADAS Features */}
        <div className="p-2">
          <div 
            className={`flex justify-between items-center p-3 rounded-md cursor-pointer ${selectedFeature === 'frontal' ? 'bg-gray-700' : 'hover:bg-gray-700'}`}
            onClick={() => setSelectedFeature('frontal')}
          >
            <span>Frontal Collision Warning</span>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={getValue('adas_frontalEnable')}
                onChange={() => handleToggle('adas_frontalEnable')}
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
            </label>
          </div>

          <div 
            className={`flex justify-between items-center p-3 rounded-md cursor-pointer ${selectedFeature === 'pedestrian' ? 'bg-gray-700' : 'hover:bg-gray-700'}`}
            onClick={() => setSelectedFeature('pedestrian')}
          >
            <span>Pedestrian Collision Warning</span>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={getValue('adas_pedestrianCollisionEnable')}
                onChange={() => handleToggle('adas_pedestrianCollisionEnable')}
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
            </label>
          </div>

          <div 
            className={`flex justify-between items-center p-3 rounded-md cursor-pointer ${selectedFeature === 'tailgating' ? 'bg-gray-700' : 'hover:bg-gray-700'}`}
            onClick={() => setSelectedFeature('tailgating')}
          >
            <span>Tailgating</span>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={getValue('adas_tailgatingEnable')}
                onChange={() => handleToggle('adas_tailgatingEnable')}
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
            </label>
          </div>

          <div 
            className={`flex justify-between items-center p-3 rounded-md cursor-pointer ${selectedFeature === 'lane' ? 'bg-gray-700' : 'hover:bg-gray-700'}`}
            onClick={() => setSelectedFeature('lane')}
          >
            <span>Lane Departure</span>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={getValue('adas_laneEnable')}
                onChange={() => handleToggle('adas_laneEnable')}
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
            </label>
          </div>

          <div 
            className={`flex justify-between items-center p-3 rounded-md cursor-pointer ${selectedFeature === 'stopandgo' ? 'bg-gray-700' : 'hover:bg-gray-700'}`}
            onClick={() => setSelectedFeature('stopandgo')}
          >
            <span>Stop and Go</span>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={getValue('adas_stopandgoEnable')}
                onChange={() => handleToggle('adas_stopandgoEnable')}
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
            </label>
          </div>
        </div>

        {/* DMS Section */}
        <div className="p-4 border-t border-b border-gray-700 mt-4">
          <h3 className="font-bold">DMS</h3>
          <p className="text-xs text-gray-400 mt-1">
            Available in vehicles with devices applying the Pro plan
          </p>
        </div>

        {/* DMS Features */}
        <div className="p-2">
          <div 
            className={`flex justify-between items-center p-3 rounded-md cursor-pointer ${selectedFeature === 'fatigue' ? 'bg-gray-700' : 'hover:bg-gray-700'}`}
            onClick={() => setSelectedFeature('fatigue')}
          >
            <span>Fatigue</span>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={getValue('dms_fatigueEnable')}
                onChange={() => handleToggle('dms_fatigueEnable')}
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
            </label>
          </div>

          <div 
            className={`flex justify-between items-center p-3 rounded-md cursor-pointer ${selectedFeature === 'seatbelt' ? 'bg-gray-700' : 'hover:bg-gray-700'}`}
            onClick={() => setSelectedFeature('seatbelt')}
          >
            <span>Seat Belt Unfastened</span>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={getValue('dms_seatbeltUnfastenEnable')}
                onChange={() => handleToggle('dms_seatbeltUnfastenEnable')}
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
            </label>
          </div>

          <div 
            className={`flex justify-between items-center p-3 rounded-md cursor-pointer ${selectedFeature === 'distracted' ? 'bg-gray-700' : 'hover:bg-gray-700'}`}
            onClick={() => setSelectedFeature('distracted')}
          >
            <span>Distracted</span>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={getValue('dms_distractionEnable')}
                onChange={() => handleToggle('dms_distractionEnable')}
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
            </label>
          </div>

          <div 
            className={`flex justify-between items-center p-3 rounded-md cursor-pointer ${selectedFeature === 'phone' ? 'bg-gray-700' : 'hover:bg-gray-700'}`}
            onClick={() => setSelectedFeature('phone')}
          >
            <span>Phone Usage</span>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={getValue('dms_phoneEnable')}
                onChange={() => handleToggle('dms_phoneEnable')}
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
            </label>
          </div>
        </div>
      </div>

      {/* Right side - configuration */}
      <div className="col-span-9 bg-white text-gray-800 rounded-lg p-6 shadow-md">
        {/* Configuration for the selected feature */}
        {renderFeatureConfig()}
      </div>
    </div>
  );

  // Render the configuration for the selected feature
  function renderFeatureConfig() {
    // Common settings for all features
    const commonSettings = (
      <>
        <div className="mb-8">
          <h3 className="text-lg font-medium mb-2">Event Trigger:</h3>
          <p className="text-sm text-gray-600">Set up the condition of triggering the event</p>
          
          <div className="mt-4 p-4 bg-gray-100 rounded-lg">
            <div className="flex justify-between mb-2">
              <div>
                <span className="text-sm font-medium">Low</span>
                <p className="text-xs text-gray-500">Less events</p>
              </div>
              <div className="text-right">
                <span className="text-sm font-medium">High</span>
                <p className="text-xs text-gray-500">More events</p>
              </div>
            </div>
            
            {/* Sensitivity slider */}
            <div className="relative mt-6 mb-2">
              <span className="text-sm font-medium mb-2 block">Sensitivity</span>
              <input 
                type="range" 
                min="0" 
                max="2" 
                step="1"
                value={getFeatureSensitivity()}
                onChange={(e) => handleSliderChange(getFeatureSensitivityKey(), parseInt(e.target.value, 10))}
                className="w-full h-2 bg-gray-300 rounded-lg appearance-none cursor-pointer"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>0</span>
                <span>1</span>
                <span>2</span>
              </div>
            </div>
          </div>
        </div>

        <div className="mb-8">
          <h3 className="text-lg font-medium mb-2">Event Recording:</h3>
          <p className="text-sm text-gray-600 mb-6">Set up the duration of the recorded video when the event is triggered, which will be captured by the device for reviewing.</p>
          
          <div className="flex items-center space-x-6 mb-4">
            <div className="w-56">
              <label className="block text-sm font-medium mb-2">Pre-event video duration (sec):</label>
              <div className="relative">
                <input 
                  type="text" 
                  value={getFeatureBeforeVideo()} 
                  onChange={(e) => handleNumberChange(getFeatureBeforeVideoKey(), e.target.value)}
                  className="block w-24 py-2 px-3 text-gray-700 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
                <div className="absolute inset-y-0 right-0 flex flex-col h-full">
                  <button 
                    className="h-1/2 px-2 bg-gray-200 hover:bg-gray-300 rounded-tr-md border-l border-gray-300"
                    onClick={() => handleNumberChange(getFeatureBeforeVideoKey(), (getFeatureBeforeVideo() + 1).toString())}
                  >
                    ▲
                  </button>
                  <button 
                    className="h-1/2 px-2 bg-gray-200 hover:bg-gray-300 rounded-br-md border-l border-t border-gray-300"
                    onClick={() => handleNumberChange(getFeatureBeforeVideoKey(), Math.max(0, getFeatureBeforeVideo() - 1).toString())}
                  >
                    ▼
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-6">
            <div className="w-56">
              <label className="block text-sm font-medium mb-2">Post-event video duration (sec):</label>
              <div className="relative">
                <input 
                  type="text" 
                  value={getFeatureAfterVideo()} 
                  onChange={(e) => handleNumberChange(getFeatureAfterVideoKey(), e.target.value)}
                  className="block w-24 py-2 px-3 text-gray-700 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
                <div className="absolute inset-y-0 right-0 flex flex-col h-full">
                  <button 
                    className="h-1/2 px-2 bg-gray-200 hover:bg-gray-300 rounded-tr-md border-l border-gray-300"
                    onClick={() => handleNumberChange(getFeatureAfterVideoKey(), (getFeatureAfterVideo() + 1).toString())}
                  >
                    ▲
                  </button>
                  <button 
                    className="h-1/2 px-2 bg-gray-200 hover:bg-gray-300 rounded-br-md border-l border-t border-gray-300"
                    onClick={() => handleNumberChange(getFeatureAfterVideoKey(), Math.max(0, getFeatureAfterVideo() - 1).toString())}
                  >
                    ▼
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="mb-4">
          <h3 className="text-lg font-medium mb-2">Auto Upload to Portal:</h3>
          <p className="text-sm text-gray-600 mb-6">Choose what kind of data to auto upload to Events for reviewing if the event is triggered.</p>
          
          <div className="p-4 bg-gray-100 rounded-lg grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">Media</label>
              <select 
                value={getFeatureMedia()}
                onChange={(e) => handleSelectChange(getFeatureMediaKey(), e.target.value)}
                className="block w-full py-2 px-3 text-gray-700 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="snapshot">Snapshot</option>
                <option value="video">Video</option>
                <option value="none">None</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2">G-sensor & GPS Data</label>
              <div className="inline-block px-3 py-2 bg-green-100 text-green-800 rounded-full">
                On
              </div>
            </div>
          </div>
        </div>

        <div className="mb-6">
          <div className="flex items-center space-x-2">
            <h3 className="text-lg font-medium">Email Notification:</h3>
            <div className="text-gray-500">
              <Info size={16} />
            </div>
          </div>
          
          <div className="mt-2">
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={getFeatureEmailNotify()}
                onChange={() => handleToggle(getFeatureEmailNotifyKey())}
              />
              <div className="w-11 h-6 bg-gray-300 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
            </label>
          </div>
        </div>

        {/* Added In-cabin Audio Alert section */}
        <div className="mt-8">
          <div className="flex items-center space-x-2">
            <h3 className="text-lg font-medium">In-cabin Audio Alert:</h3>
          </div>
          
          <div className="mt-2">
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={getFeatureAudioAlert()}
                onChange={() => handleToggle(getFeatureAudioAlertKey())}
              />
              <div className="w-11 h-6 bg-gray-300 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
            </label>
          </div>
        </div>
      </>
    );

    return commonSettings;
  }

  // Helper functions to get the appropriate keys for the selected feature
  function getFeatureSensitivityKey(): string {
    const map: { [key: string]: string } = {
      'frontal': 'frontal_sensitive',
      'pedestrian': 'pedestrianCollision_sensitive',
      'tailgating': 'tailgating_sensitive',
      'lane': 'lane_sensitive',
      'stopandgo': 'stopandgo_sensitive',
      'fatigue': 'fatigue_sensitive',
      'seatbelt': 'seatbeltUnfasten_sensitive',
      'distracted': 'distraction_sensitive',
      'phone': 'phone_sensitive'
    };
    return map[selectedFeature] || 'frontal_sensitive';
  }

  function getFeatureSensitivity(): number {
    return getValue(getFeatureSensitivityKey());
  }

  function getFeatureBeforeVideoKey(): string {
    const map: { [key: string]: string } = {
      'frontal': 'frontal_videoBefore',
      'pedestrian': 'pedestrianCollision_videoBefore',
      'tailgating': 'tailgating_videoBefore',
      'lane': 'lane_videoBefore',
      'stopandgo': 'stopandgo_videoBefore',
      'fatigue': 'fatigue_videoBefore',
      'seatbelt': 'seatbeltUnfasten_videoBefore',
      'distracted': 'distraction_videoBefore',
      'phone': 'phone_videoBefore'
    };
    return map[selectedFeature] || 'frontal_videoBefore';
  }

  function getFeatureBeforeVideo(): number {
    return getValue(getFeatureBeforeVideoKey());
  }

  function getFeatureAfterVideoKey(): string {
    const map: { [key: string]: string } = {
      'frontal': 'frontal_videoAfter',
      'pedestrian': 'pedestrianCollision_videoAfter',
      'tailgating': 'tailgating_videoAfter',
      'lane': 'lane_videoAfter',
      'stopandgo': 'stopandgo_videoAfter',
      'fatigue': 'fatigue_videoAfter',
      'seatbelt': 'seatbeltUnfasten_videoAfter',
      'distracted': 'distraction_videoAfter',
      'phone': 'phone_videoAfter'
    };
    return map[selectedFeature] || 'frontal_videoAfter';
  }

  function getFeatureAfterVideo(): number {
    return getValue(getFeatureAfterVideoKey());
  }

  function getFeatureMediaKey(): string {
    const map: { [key: string]: string } = {
      'frontal': 'frontal_media',
      'pedestrian': 'pedestrianCollision_media',
      'tailgating': 'tailgating_media',
      'lane': 'lane_media',
      'stopandgo': 'stopandgo_media',
      'fatigue': 'fatigue_media',
      'seatbelt': 'seatbeltUnfasten_media',
      'distracted': 'distraction_media',
      'phone': 'phone_media'
    };
    return map[selectedFeature] || 'frontal_media';
  }

  function getFeatureMedia(): string {
    return getValue(getFeatureMediaKey());
  }

  function getFeatureEmailNotifyKey(): string {
    const map: { [key: string]: string } = {
      'frontal': 'frontal_emailNotify',
      'pedestrian': 'pedestrianCollision_emailNotify',
      'tailgating': 'tailgating_emailNotify',
      'lane': 'lane_emailNotify',
      'stopandgo': 'stopandgo_emailNotify',
      'fatigue': 'fatigue_emailNotify',
      'seatbelt': 'seatbeltUnfasten_emailNotify',
      'distracted': 'distraction_emailNotify',
      'phone': 'phone_emailNotify'
    };
    return map[selectedFeature] || 'frontal_emailNotify';
  }

  function getFeatureEmailNotify(): boolean {
    return getValue(getFeatureEmailNotifyKey());
  }

  // New functions for In-cabin Audio Alert
  function getFeatureAudioAlertKey(): string {
    const map: { [key: string]: string } = {
      'frontal': 'frontal_audioAlert',
      'pedestrian': 'pedestrianCollision_audioAlert',
      'tailgating': 'tailgating_audioAlert',
      'lane': 'lane_audioAlert',
      'stopandgo': 'stopandgo_audioAlert',
      'fatigue': 'fatigue_audioAlert',
      'seatbelt': 'seatbeltUnfasten_audioAlert',
      'distracted': 'distraction_audioAlert',
      'phone': 'phone_audioAlert'
    };
    return map[selectedFeature] || 'frontal_audioAlert';
  }

  function getFeatureAudioAlert(): boolean {
    return getValue(getFeatureAudioAlertKey());
  }
};

export default AIEventSettings;