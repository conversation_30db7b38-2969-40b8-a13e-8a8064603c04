import React, { useEffect, useState } from 'react';
import { VisionMaxAPI, DashboardResponse, HighLightListResponse, NewDashboardResponse, NewDeviceResponse } from '../../api/visionMaxApi';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/common/Card';
import { DeviceStatusChart } from '../../components/charts/DeviceStatusChart';
import { TripStatsChart } from '../../components/charts/TripStatsChart';
import { LiveFleetMap, Asset } from '../../components/maps/LiveFleetMap';
import { Spinner } from '../../components/common/Spinner';
import { Alert } from '../../components/common/Alert';

// Asset Selection Components
interface AssetListProps {
  assets: Asset[];
  selectedAssetId?: string;
  onAssetSelect: (assetId: string) => void;
  onClose?: () => void;
  className?: string;
}

const AssetListSidebar: React.FC<AssetListProps> = ({
  assets,
  selectedAssetId,
  onAssetSelect,
  onClose,
  className = ''
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Online': return { icon: '🟢', color: 'text-green-600' };
      case 'Offline': return { icon: '🔴', color: 'text-red-600' };
      case 'Idle': return { icon: '🟡', color: 'text-yellow-600' };
      case 'Maintenance': return { icon: '🔵', color: 'text-blue-600' };
      default: return { icon: '⚪', color: 'text-gray-600' };
    }
  };

  const formatLastUpdate = (timestamp?: string) => {
    if (!timestamp) return 'Unknown';
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`;
    return date.toLocaleDateString();
  };

  const filteredAssets = assets.filter(asset => {
    const name = (asset.name || asset.deviceId).toLowerCase();
    const licensePlate = (asset.licensePlate || '').toLowerCase();
    const search = searchTerm.toLowerCase();
    return name.includes(search) || licensePlate.includes(search);
  });

  return (
    <div className={`bg-white border-r border-gray-200 overflow-y-auto ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200 flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">
          Fleet Assets ({assets.length})
        </h3>
        {onClose && (
          <button
            onClick={onClose}
            className="p-1 rounded-lg hover:bg-gray-100 transition-colors"
          >
            ✕
          </button>
        )}
      </div>

      {/* Search */}
      <div className="p-4 border-b border-gray-200">
        <input
          type="text"
          placeholder="Search vehicles..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      {/* Asset List */}
      <div className="divide-y divide-gray-200">
        {filteredAssets.map((asset) => {
          const statusInfo = getStatusIcon(asset.status);
          const isSelected = selectedAssetId === asset.id;
          
          return (
            <div
              key={asset.id}
              onClick={() => onAssetSelect(asset.id)}
              className={`p-4 cursor-pointer transition-colors hover:bg-gray-50 ${
                isSelected ? 'bg-blue-50 border-r-4 border-blue-500' : ''
              }`}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  {/* Vehicle Name/ID */}
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">{statusInfo.icon}</span>
                    <h4 className="text-sm font-medium text-gray-900 truncate">
                      {asset.name || asset.deviceId}
                    </h4>
                  </div>
                  
                  {/* Status and Details */}
                  <div className="mt-1 flex items-center space-x-4 text-xs text-gray-500">
                    <span className={`font-medium ${statusInfo.color}`}>
                      {asset.status}
                    </span>
                    {asset.vehicleType && (
                      <span>
                        {asset.vehicleType.charAt(0).toUpperCase() + asset.vehicleType.slice(1)}
                      </span>
                    )}
                    {asset.licensePlate && (
                      <span className="font-mono">{asset.licensePlate}</span>
                    )}
                  </div>
                  
                  {/* Activity Info */}
                  <div className="mt-1 text-xs text-gray-500">
                    {asset.isOngoing && (
                      <span className="inline-flex items-center px-2 py-0.5 rounded-full bg-green-100 text-green-800 mr-2">
                        🚛 On Trip
                      </span>
                    )}
                    <span>Updated {formatLastUpdate(asset.lastUpdate)}</span>
                  </div>
                </div>
                
                {/* Quick Info */}
                <div className="flex flex-col space-y-1 items-end">
                  {asset.speed !== undefined && asset.speed > 0 && (
                    <span className="text-xs font-medium text-green-600">
                      {asset.speed} km/h
                    </span>
                  )}
                  {isSelected && (
                    <span className="text-xs text-blue-600 font-medium">
                      Selected
                    </span>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>
      
      {filteredAssets.length === 0 && (
        <div className="p-8 text-center text-gray-500">
          <div className="text-4xl mb-2">🚛</div>
          <div className="text-sm">
            {searchTerm ? 'No vehicles match your search' : 'No vehicles found'}
          </div>
        </div>
      )}
    </div>
  );
};

// Asset Dropdown Component
interface AssetDropdownProps {
  assets: Asset[];
  selectedAssetId?: string;
  onAssetSelect: (assetId: string) => void;
  placeholder?: string;
  className?: string;
}

const AssetDropdown: React.FC<AssetDropdownProps> = ({
  assets,
  selectedAssetId,
  onAssetSelect,
  placeholder = "Select a vehicle...",
  className = ''
}) => {
  return (
    <select
      value={selectedAssetId || ''}
      onChange={(e) => onAssetSelect(e.target.value)}
      className={`px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white ${className}`}
    >
      <option value="">{placeholder}</option>
      {assets.map((asset) => (
        <option key={asset.id} value={asset.id}>
          {asset.status === 'Online' ? '🟢' : 
           asset.status === 'Idle' ? '🟡' :
           asset.status === 'Offline' ? '🔴' : '🔵'} {asset.name || asset.deviceId}
          {asset.licensePlate ? ` (${asset.licensePlate})` : ''}
        </option>
      ))}
    </select>
  );
};

// Quick Asset Filter Buttons
interface AssetFilterProps {
  assets: Asset[];
  activeFilter: string;
  onFilterChange: (filter: string) => void;
  className?: string;
}

const AssetStatusFilter: React.FC<AssetFilterProps> = ({
  assets,
  activeFilter,
  onFilterChange,
  className = ''
}) => {
  const statusCounts = {
    'All': assets.length,
    'Online': assets.filter(a => a.status === 'Online').length,
    'Offline': assets.filter(a => a.status === 'Offline').length,
    'Idle': assets.filter(a => a.status === 'Idle').length,
    'Maintenance': assets.filter(a => a.status === 'Maintenance').length
  };

  const statusColors = {
    'All': 'bg-gray-100 text-gray-800',
    'Online': 'bg-green-100 text-green-800',
    'Offline': 'bg-red-100 text-red-800',
    'Idle': 'bg-yellow-100 text-yellow-800',
    'Maintenance': 'bg-blue-100 text-blue-800'
  };

  return (
    <div className={`flex flex-wrap gap-2 ${className}`}>
      {Object.entries(statusCounts).map(([status, count]) => (
        <button
          key={status}
          onClick={() => onFilterChange(status)}
          className={`px-3 py-1 rounded-lg text-sm font-medium transition-all ${
            activeFilter === status 
              ? 'ring-2 ring-blue-500 ring-offset-1' 
              : 'hover:shadow-md'
          } ${statusColors[status as keyof typeof statusColors]}`}
        >
          {status} ({count})
        </button>
      ))}
    </div>
  );
};

const Dashboard: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [dashboardData, setDashboardData] = useState<NewDashboardResponse | null>(null);
  const [highlightData, setHighlightData] = useState<HighLightListResponse | null>(null);
  const [deviceData, setDeviceData] = useState<NewDeviceResponse | null>(null);
  const [fleetData, setFleetData] = useState<{
    assets: Asset[],
    mapCenter: { lat: number, lng: number },
    mapZoom: number
  }>({
    assets: [],
    mapCenter: { lat: 39.8283, lng: -98.5795 },
    mapZoom: 4
  });

  // Asset Selection State
  const [selectedAssetId, setSelectedAssetId] = useState<string>('');
  const [showAssetList, setShowAssetList] = useState(false);
  const [statusFilter, setStatusFilter] = useState<string>('All');

  const token = localStorage.getItem('authToken');

  // Create API instance
  const api = new VisionMaxAPI({
    baseURL: 'https://api.visionmaxfleet.com/',
    token
  });

  // Helper function to determine device status based on multiple factors
  const getDeviceStatus = (device: any): 'Online' | 'Offline' | 'Idle' | 'Maintenance' => {
    if (device.connectivity_state === 'standby') {
      return 'Idle';
    }
    if (!device.is_online) {
      return 'Offline';
    }
    
    if (device.cover_status === 'maintenance' || device.cover_status === 'removed') {
      return 'Maintenance';
    }
    
    const now = new Date().getTime();
    const lastUpdate = new Date(device.latest_timestamp).getTime();
    const hoursSinceUpdate = (now - lastUpdate) / (1000 * 60 * 60);
    
 
    
    if (device.is_online) {
      return 'Online';
    }
    
    return 'Online';
  };

  // Helper function to map vehicle type ID to vehicle type string
  const getVehicleType = (vehicleTypeId: number): 'truck' | 'van' | 'car' | 'other' => {
    switch (vehicleTypeId) {
      case 1: return 'car';
      case 2: return 'van';
      case 3: return 'truck';
      default: return 'truck';
    }
  };

  const fetchData = async () => {
    setIsLoading(true);
    setHasError(false);
    
    try {
      const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone || 'UTC';
      
      // Fetch dashboard data
      const dashboard = await api.getDashboard(timezone);
      setDashboardData(dashboard);
      
      // Fetch highlight data
      const highlights = await api.getHighlightList({ limit: 5 });
      setHighlightData(highlights);
      
      // Fetch device data
      let assets: any[] = [];
      try {
        const devices = await api.getDevices({ limit: 100 });
        setDeviceData(devices);
        
        if (devices && Array.isArray(devices.data)) {
          // Transform devices into assets for the Google Maps component
          assets = devices.data.map(device => ({
            id: device.device_id.toString(),
            deviceId: device.device_id.toString(),
            name: device.device_name || device.name,
            serialNumber: device.serial_number,
            status: getDeviceStatus(device),
            position: {
              lat: device.latest_lat || 0,
              lng: device.latest_lng || 0
            },
            connectivity_state: device.connectivity_state,
            heading: 0,
            speed: 0,
            lastUpdate: device.latest_timestamp,
            lastConnected: device.last_connected_timestamp ? new Date(device.last_connected_timestamp).toISOString() : undefined,
            firstConnected: device.first_connected_timestamp ? new Date(device.first_connected_timestamp).toISOString() : undefined,
            driverName: undefined,
            driverId: undefined,
            vehicleId: device.vehicle_id,
            vehicleType: getVehicleType(device.vehicle_type_id),
            licensePlate: device.license_plate,
            vin: device.vehicle_identification_number,
            groupId: device.group_id,
            groupName: device.group_name,
            firmwareVersion: device.firmware_version,
            applicationVersion: device.application_version,
            planType: device.plan_type,
            productName: device.product_name,
            skuName: device.sku_name,
            region: device.region,
            isOngoing: device.onging_live_trip === 'true',
            eventCount: device.event_count,
            tripCount: device.trip_count,
            latestTripId: device.latest_trips_id,
            latestTripStatus: device.latest_trips_status,
            latestEventId: device.latest_event_id,
            latestEventType: device.latest_event_type,
            coverStatus: device.cover_status,
            installPosition: device.install_position,
            driverPosition: device.driver_position,
            IMEI_1: device.IMEI_1,
            IMEI_2: device.IMEI_2,
            totalPhoto: device.total_photo,
            liveviewSession: device.liveview_session,
            snapshot: device.snapshot
          }));
        }
      } catch (deviceError) {
        console.error('Error fetching device data:', deviceError);
      }

      // Calculate map center based on devices
      const validCoordinates = assets.filter(asset => 
        asset.position.lat !== 0 && 
        asset.position.lng !== 0 &&
        !isNaN(asset.position.lat) && 
        !isNaN(asset.position.lng)
      );
      
      let mapCenter = { lat: 39.8283, lng: -98.5795 };
      let mapZoom = 4;
      
      if (validCoordinates.length > 0) {
        const totalLat = validCoordinates.reduce((sum, asset) => sum + asset.position.lat, 0);
        const totalLng = validCoordinates.reduce((sum, asset) => sum + asset.position.lng, 0);
        mapCenter = {
          lat: totalLat / validCoordinates.length,
          lng: totalLng / validCoordinates.length
        };
        mapZoom = validCoordinates.length === 1 ? 12 : 8;
      }
      
      setFleetData({
        assets,
        mapCenter,
        mapZoom
      });
        
      setIsLoading(false);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setHasError(true);
      setIsLoading(false);
    }
  };

  // Filter assets based on status
  const filteredAssets = statusFilter === 'All' 
    ? fleetData.assets 
    : fleetData.assets.filter(asset => asset.status === statusFilter);

  // Handle asset selection
  const handleAssetSelect = (assetId: string) => {
    setSelectedAssetId(assetId);
    // Close asset list if on mobile
    if (window.innerWidth < 768) {
      setShowAssetList(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchData();
    
    // Refresh data every 2 minutes
    const interval = setInterval(() => {
      fetchData();
    }, 1200000);
    
    return () => clearInterval(interval);
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <Spinner size="large" />
      </div>
    );
  }

  if (hasError) {
    return (
      <Alert
        type="error"
        title="Error Loading Dashboard"
        message="There was a problem loading the dashboard data. Please try again."
        action={{
          label: 'Retry',
          onClick: fetchData
        }}
      />
    );
  }

  // Calculate stats from dashboard data
  const deviceCount = dashboardData?.data?.all_device || 0;
  const onlineDevices = dashboardData?.data?.online || 0;
  const tripSummary = dashboardData?.data?.trip_summary;

  const drivingTimeHours = tripSummary ? (tripSummary.driving_time_in_seconds / 3600).toFixed(2) : "0";
  const distanceKm = tripSummary ? (tripSummary.total_distance_in_metric / 1000).toFixed(2) : "0";
  const avgTripDistanceKm = tripSummary && tripSummary.trips > 0 
    ? ((tripSummary.total_distance_in_metric / 1000) / tripSummary.trips).toFixed(2)
    : "0";
    
  const tripStatsData = tripSummary ? [
    {
      date: new Date().toLocaleDateString(),
      trips: tripSummary.trips || 0,
      distance: tripSummary.total_distance_in_metric / 1000,
      duration: tripSummary.driving_time_in_seconds / 3600
    }
  ] : [];

  return (
    <div className="space-y-6">
      {/* Existing Dashboard Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Online Devices</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <div className="text-5xl font-bold">{onlineDevices}</div>
              <div className="ml-4 text-xl">/ {deviceCount}</div>
            </div>
            <div className="mt-4">
              <DeviceStatusChart 
                online={onlineDevices} 
                offline={deviceCount - onlineDevices}
              />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Trip Summary (Last 7 Days)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <div className="text-sm text-gray-500">Trips</div>
                <div className="text-3xl font-bold">{tripSummary?.trips || 0}</div>
              </div>
              <div>
                <div className="text-sm text-gray-500">Distance (KM)</div>
                <div className="text-3xl font-bold">{distanceKm}</div>
              </div>
              <div>
                <div className="text-sm text-gray-500">Driving Time (Hours)</div>
                <div className="text-3xl font-bold">{drivingTimeHours}</div>
              </div>
              <div>
                <div className="text-sm text-gray-500">Avg Trip (KM)</div>
                <div className="text-3xl font-bold">{avgTripDistanceKm}</div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Fleet Highlights</CardTitle>
          </CardHeader>
          <CardContent>
            {tripStatsData.length > 0 ? (
              <div>
                <TripStatsChart data={tripStatsData} />
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-40 text-gray-500">
                <svg className="w-12 h-12 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>There's no highlight to display</span>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
      
      {/* Enhanced Fleet Map with Asset Selection */}
      <Card>
        <CardHeader>
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <CardTitle>Live Fleet Status</CardTitle>
            
            {/* Asset Selection Controls */}
            <div className="flex flex-col sm:flex-row gap-4">
              {/* Asset Dropdown */}
              <AssetDropdown
                assets={fleetData.assets}
                selectedAssetId={selectedAssetId}
                onAssetSelect={handleAssetSelect}
                className="min-w-64"
              />
              
              {/* Control Buttons */}
              <div className="flex gap-2">
                <button
                  onClick={() => setShowAssetList(!showAssetList)}
                  className={`px-3 py-2 rounded-lg border transition-colors whitespace-nowrap ${
                    showAssetList 
                      ? 'bg-blue-500 text-white border-blue-500' 
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  📋 Asset List
                </button>
                
                {selectedAssetId && (
                  <button
                    onClick={() => setSelectedAssetId('')}
                    className="px-3 py-2 rounded-lg bg-gray-500 text-white hover:bg-gray-600 transition-colors whitespace-nowrap"
                  >
                    ❌ Clear
                  </button>
                )}
              </div>
            </div>
          </div>
          
          {/* Status Filter */}
          <div className="mt-4">
            <AssetStatusFilter
              assets={fleetData.assets}
              activeFilter={statusFilter}
              onFilterChange={setStatusFilter}
            />
          </div>
        </CardHeader>
        
        <CardContent className="p-0 relative">
          <div className="flex h-96">
            {/* Asset List Sidebar */}
            {showAssetList && (
              <AssetListSidebar
                assets={filteredAssets}
                selectedAssetId={selectedAssetId}
                onAssetSelect={handleAssetSelect}
                onClose={() => setShowAssetList(false)}
                className="w-80 h-full"
              />
            )}
            
            {/* Map */}
            <div className="flex-1">
              <LiveFleetMap 
                assets={filteredAssets || []} 
                selectedAssetId={selectedAssetId}
                center={fleetData.mapCenter}
                zoom={fleetData.mapZoom}
                onAssetClick={handleAssetSelect}
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Dashboard;